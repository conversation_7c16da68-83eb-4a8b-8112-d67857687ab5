/**
 * 简化系统测试 - 专注于核心功能验证
 *
 * 这个测试专注于验证系统的核心逻辑，避免复杂的依赖问题
 */

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");

class SimpleSystemTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();

    // 测试配置
    this.config = {
      baseUrl: "http://localhost:6789",
      timeout: 30000,
      concurrentCount: 10, // 减少并发数量以便观察
    };
  }

  /**
   * 执行简化系统测试
   */
  async runSimpleTests() {
    console.log("🚀 开始执行简化系统测试");
    console.log("=".repeat(80));

    try {
      // 1. 系统健康检查
      await this.testSystemHealth();

      // 2. 基础功能测试
      await this.testBasicFunctionality();

      // 3. 边界条件测试
      await this.testBoundaryConditions();

      // 4. 并发测试
      await this.testConcurrency();

      // 5. 生成测试报告
      this.generateReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.logTestResult("SYSTEM_FAILURE", {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 测试系统健康状态
   */
  async testSystemHealth() {
    console.log("\n📊 测试1: 系统健康检查");
    console.log("-".repeat(50));

    const testCase = {
      name: "系统健康检查",
      startTime: Date.now(),
    };

    try {
      const response = await axios.get(`${this.config.baseUrl}/health`, {
        timeout: this.config.timeout,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.response = response.data;
      testCase.httpStatus = response.status;

      console.log("✅ 系统健康检查通过");
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 响应内容:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorCode = error.code;

      console.log("❌ 系统健康检查失败");
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);

      // 如果健康检查失败，可能是系统未启动
      if (error.code === "ECONNREFUSED") {
        console.log("⚠️ 系统可能未启动，尝试启动系统...");
        await this.attemptSystemStart();
      }
    }

    this.logTestResult("HEALTH_CHECK", testCase);
  }

  /**
   * 尝试启动系统
   */
  async attemptSystemStart() {
    console.log("🔄 尝试启动AI招聘助手系统...");

    try {
      // 这里我们只是等待一段时间，实际环境中可能需要其他启动逻辑
      await this.delay(5000);

      // 再次检查健康状态
      const response = await axios.get(`${this.config.baseUrl}/health`, {
        timeout: 5000,
      });

      if (response.status === 200) {
        console.log("✅ 系统启动成功");
        return true;
      }
    } catch (error) {
      console.log("❌ 系统启动失败:", error.message);
      return false;
    }

    return false;
  }

  /**
   * 测试基础功能
   */
  async testBasicFunctionality() {
    console.log("\n🔧 测试2: 基础功能测试");
    console.log("-".repeat(50));

    const basicTests = [
      {
        name: "初始化对话测试",
        input: { message: "__INIT__", userEmail: "<EMAIL>" },
        expectedSuccess: true,
      },
      {
        name: "简单问候测试",
        input: { message: "你好", userEmail: "<EMAIL>" },
        expectedSuccess: true,
      },
      {
        name: "职位询问测试",
        input: { message: "有什么职位推荐吗", userEmail: "<EMAIL>" },
        expectedSuccess: true,
      },
      {
        name: "信息提供测试",
        input: {
          message: "我是Java开发工程师，有3年经验",
          userEmail: "<EMAIL>",
        },
        expectedSuccess: true,
      },
    ];

    for (const test of basicTests) {
      await this.executeBasicTest(test);
      await this.delay(500); // 测试间隔
    }
  }

  /**
   * 执行基础功能测试
   */
  async executeBasicTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

      const response = await axios.post(
        `${this.config.baseUrl}/api/chat`,
        {
          ...testCase.input,
          sessionId: sessionId,
        },
        {
          timeout: this.config.timeout,
          headers: { "Content-Type": "application/json" },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 验证响应结构
      const isValidResponse = this.validateResponse(response.data);
      testCase.responseValid = isValidResponse;

      if (isValidResponse) {
        console.log(`✅ 测试通过: ${testCase.name}`);
      } else {
        console.log(`⚠️ 测试通过但响应格式异常: ${testCase.name}`);
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 响应摘要:`, this.getResponseSummary(response.data));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorCode = error.code;
      testCase.httpStatus = error.response?.status || "TIMEOUT";

      console.log(`❌ 测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);

      if (error.response) {
        console.log(
          `📋 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
      }
    }

    this.logTestResult("BASIC_FUNCTIONALITY", testCase);
  }

  /**
   * 测试边界条件
   */
  async testBoundaryConditions() {
    console.log("\n🎯 测试3: 边界条件测试");
    console.log("-".repeat(50));

    const boundaryTests = [
      {
        name: "空消息测试",
        input: { message: "", userEmail: "<EMAIL>" },
        expectedError: true,
      },
      {
        name: "超长消息测试",
        input: { message: "A".repeat(5000), userEmail: "<EMAIL>" },
        expectedError: false,
      },
      {
        name: "特殊字符测试",
        input: {
          message: "!@#$%^&*()_+{}[]|\\:\";'<>?,./",
          userEmail: "<EMAIL>",
        },
        expectedError: false,
      },
      {
        name: "中文测试",
        input: {
          message: "你好世界！这是一个中文测试。",
          userEmail: "<EMAIL>",
        },
        expectedError: false,
      },
    ];

    for (const test of boundaryTests) {
      await this.executeBoundaryTest(test);
      await this.delay(500);
    }
  }

  /**
   * 执行边界条件测试
   */
  async executeBoundaryTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行边界测试: ${testCase.name}`);

      const response = await axios.post(
        `${this.config.baseUrl}/api/chat`,
        {
          ...testCase.input,
          sessionId: sessionId,
        },
        {
          timeout: this.config.timeout,
          validateStatus: () => true, // 接受所有状态码
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 分析错误处理结果
      const isError =
        response.status >= 400 || (response.data && !response.data.success);
      testCase.actualError = isError;
      testCase.errorHandlingCorrect = isError === testCase.expectedError;

      if (testCase.errorHandlingCorrect) {
        testCase.status = "SUCCESS";
        console.log(`✅ 边界测试通过: ${testCase.name}`);
      } else {
        testCase.status = "FAILURE";
        console.log(`❌ 边界测试失败: ${testCase.name}`);
      }

      console.log(
        `🎯 错误处理: 预期=${testCase.expectedError}, 实际=${isError}`
      );
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "NETWORK_ERROR";
      testCase.error = error.message;

      console.log(`❌ 边界测试网络错误: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("BOUNDARY_CONDITIONS", testCase);
  }

  /**
   * 测试并发性能
   */
  async testConcurrency() {
    console.log("\n🚀 测试4: 并发性能测试");
    console.log("-".repeat(50));

    const concurrentTests = [];
    const testMessages = [
      "你好，我想找工作",
      "我是Java开发工程师",
      "有什么职位推荐吗",
      "我在阿里巴巴工作",
      "期望薪资30k",
    ];

    console.log(`🎯 准备发起 ${this.config.concurrentCount} 个并发请求...`);

    // 创建并发测试任务
    for (let i = 0; i < this.config.concurrentCount; i++) {
      const testCase = {
        name: `并发测试-${i + 1}`,
        sessionId: uuidv4(),
        message: testMessages[i % testMessages.length],
        userEmail: `concurrent-${i}@test.com`,
        testIndex: i,
      };

      concurrentTests.push(this.executeConcurrentTest(testCase));
    }

    const startTime = Date.now();
    console.log(`⏰ 开始时间: ${new Date(startTime).toISOString()}`);

    // 执行所有并发测试
    const results = await Promise.allSettled(concurrentTests);

    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // 分析并发测试结果
    const successCount = results.filter(
      (r) => r.status === "fulfilled" && r.value.status === "SUCCESS"
    ).length;
    const failureCount = results.filter(
      (r) =>
        r.status === "rejected" ||
        (r.status === "fulfilled" && r.value.status !== "SUCCESS")
    ).length;
    const successRate = (
      (successCount / this.config.concurrentCount) *
      100
    ).toFixed(2);

    console.log("\n📊 并发测试结果统计:");
    console.log(`⏰ 总耗时: ${totalTime}ms`);
    console.log(`✅ 成功请求: ${successCount}/${this.config.concurrentCount}`);
    console.log(`❌ 失败请求: ${failureCount}/${this.config.concurrentCount}`);
    console.log(`📈 成功率: ${successRate}%`);
    console.log(
      `⚡ 平均响应时间: ${(totalTime / this.config.concurrentCount).toFixed(
        2
      )}ms`
    );

    // 记录并发测试结果
    const concurrentTestResult = {
      name: "并发性能测试",
      startTime: startTime,
      endTime: endTime,
      totalTime: totalTime,
      concurrentCount: this.config.concurrentCount,
      successCount: successCount,
      failureCount: failureCount,
      successRate: parseFloat(successRate),
      averageResponseTime: totalTime / this.config.concurrentCount,
    };

    this.logTestResult("CONCURRENT_PERFORMANCE", concurrentTestResult);
  }

  /**
   * 执行单个并发测试
   */
  async executeConcurrentTest(testCase) {
    testCase.startTime = Date.now();

    try {
      const response = await axios.post(
        `${this.config.baseUrl}/api/chat`,
        {
          message: testCase.message,
          userEmail: testCase.userEmail,
          sessionId: testCase.sessionId,
        },
        {
          timeout: this.config.timeout,
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      return testCase;
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.httpStatus = error.response?.status || "TIMEOUT";

      return testCase;
    }
  }

  /**
   * 验证响应格式
   */
  validateResponse(responseData) {
    if (!responseData || typeof responseData !== "object") {
      return false;
    }

    // 检查必需字段
    const requiredFields = ["success", "timestamp"];
    return requiredFields.every((field) => field in responseData);
  }

  /**
   * 获取响应摘要
   */
  getResponseSummary(responseData) {
    if (!responseData) return "No response";

    return {
      success: responseData.success,
      hasResponse: !!responseData.response,
      responseType: responseData.response?.type || "unknown",
      contentLength: responseData.response?.content?.length || 0,
    };
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log("\n" + "=".repeat(80));
    console.log("📊 简化系统测试 - 详细报告");
    console.log("=".repeat(80));

    const totalTime = Date.now() - this.startTime;
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(
      (t) => t.status === "SUCCESS"
    ).length;
    const failedTests = this.testResults.filter(
      (t) => t.status !== "SUCCESS"
    ).length;
    const successRate =
      totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(2) : 0;

    console.log(`\n📈 总体测试统计:`);
    console.log(
      `⏰ 总测试时间: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}秒)`
    );
    console.log(`🧪 总测试数量: ${totalTests}`);
    console.log(`✅ 成功测试: ${successfulTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📊 成功率: ${successRate}%`);

    // 按类别统计
    const categoryStats = {};
    this.testResults.forEach((test) => {
      if (!categoryStats[test.category]) {
        categoryStats[test.category] = { total: 0, success: 0, failure: 0 };
      }
      categoryStats[test.category].total++;
      if (test.status === "SUCCESS") {
        categoryStats[test.category].success++;
      } else {
        categoryStats[test.category].failure++;
      }
    });

    console.log(`\n📋 分类测试统计:`);
    Object.entries(categoryStats).forEach(([category, stats]) => {
      const categorySuccessRate = ((stats.success / stats.total) * 100).toFixed(
        2
      );
      console.log(
        `  ${category}: ${stats.success}/${stats.total} (${categorySuccessRate}%)`
      );
    });

    // 性能分析
    const responseTimes = this.testResults
      .filter((t) => t.responseTime && t.responseTime > 0)
      .map((t) => t.responseTime);

    if (responseTimes.length > 0) {
      const avgResponseTime =
        responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);

      console.log(`\n⚡ 性能分析:`);
      console.log(`   平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   最大响应时间: ${maxResponseTime}ms`);
      console.log(`   最小响应时间: ${minResponseTime}ms`);
    }

    // 详细测试结果
    console.log(`\n📝 详细测试结果:`);
    this.testResults.forEach((test, index) => {
      console.log(
        `\n${index + 1}. ${test.category} - ${test.name || "未命名测试"}`
      );
      console.log(`   状态: ${test.status}`);
      console.log(`   时间: ${test.responseTime || test.totalTime || "N/A"}ms`);

      if (test.status === "SUCCESS") {
        console.log(`   ✅ 测试通过`);
      } else {
        console.log(`   ❌ 测试失败: ${test.error || "未知错误"}`);
      }

      if (test.httpStatus) {
        console.log(`   HTTP状态: ${test.httpStatus}`);
      }

      // 显示关键测试数据
      if (test.rawResponse && test.rawResponse.success !== undefined) {
        console.log(`   响应成功: ${test.rawResponse.success}`);
        if (test.rawResponse.response?.type) {
          console.log(`   响应类型: ${test.rawResponse.response.type}`);
        }
      }
    });

    // 测试结论
    console.log(`\n🎯 测试结论:`);
    if (successRate >= 90) {
      console.log(`   ✅ 系统运行状态良好 (成功率: ${successRate}%)`);
    } else if (successRate >= 70) {
      console.log(`   ⚠️ 系统运行状态一般 (成功率: ${successRate}%)`);
    } else {
      console.log(`   ❌ 系统运行状态较差 (成功率: ${successRate}%)`);
    }

    // 未测试区域
    console.log(`\n🚫 未测试区域:`);
    const untestedAreas = [
      "数据库事务一致性测试",
      "AI模型响应质量评估",
      "用户会话持久化测试",
      "推荐算法准确性测试",
      "大规模并发压力测试 (1000+)",
      "长期运行稳定性测试",
    ];

    untestedAreas.forEach((area) => {
      console.log(`   ⚠️ ${area} - 需要专门的测试环境和数据`);
    });

    console.log("\n" + "=".repeat(80));
    console.log("📊 测试报告生成完成");
    console.log("=".repeat(80));
  }

  /**
   * 记录测试结果
   */
  logTestResult(category, testCase) {
    this.testResults.push({
      category,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new SimpleSystemTester();
  tester.runSimpleTests().catch(console.error);
}

module.exports = SimpleSystemTester;
