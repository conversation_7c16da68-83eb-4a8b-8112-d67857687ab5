{"testId": "integration-test-1753966633548", "startTime": "2025-07-31T12:57:13.548Z", "endTime": "2025-07-31T13:02:42.891Z", "totalTestTime": 329343, "statistics": {"totalTests": 25, "passedTests": 23, "failedTests": 2, "testSuccessRate": 92, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 27934.108108108107, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 27934.108108108107, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:13.624Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753966633559, "endTime": 1753966633624, "responseTime": 65, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T12:57:13.615Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:20.839Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "04507722-021d-4106-bf42-35aa0e314dbb"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753966633625, "category": "DUAL_MODEL_AI", "endTime": 1753966640838, "responseTime": 7213, "httpStatus": 200, "responseData": {"success": true, "sessionId": "04507722-021d-4106-bf42-35aa0e314dbb", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T12:57:20.835Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:27.505Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "1fc397be-e8a8-4219-b76d-93ccabeda9e2"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753966641841, "category": "DUAL_MODEL_AI", "endTime": 1753966647504, "responseTime": 5663, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1fc397be-e8a8-4219-b76d-93ccabeda9e2", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T12:57:27.502Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:45.163Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "efc63c02-ecb8-47fe-9447-e7e0ce2545f6"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753966648506, "category": "DUAL_MODEL_AI", "endTime": 1753966665162, "responseTime": 16656, "httpStatus": 200, "responseData": {"success": true, "sessionId": "efc63c02-ecb8-47fe-9447-e7e0ce2545f6", "response": {"type": "first_ai_response", "content": "明白了，您目前关注大模型方向的职位机会。为了更好地为您推荐合适的岗位，能否请您补充以下信息：  \n\n1. 您的技术栈或具体研究方向（如NLP、多模态、模型训练优化等）  \n2. 当前公司及职级（如：中级算法工程师/研究员）  \n3. 期望的薪资范围和优先考虑的工作地点  \n\n这些信息将帮助我更有针对性地为您匹配机会。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T12:57:45.157Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 159, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:52.934Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "e8564ef2-f047-45b9-b37b-f2e8b42d22ad"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753966666164, "category": "DUAL_MODEL_AI", "endTime": 1753966672933, "responseTime": 6769, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e8564ef2-f047-45b9-b37b-f2e8b42d22ad", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T12:57:52.156Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:57:52.930Z"}, "analysis": {"hasResponse": true, "responseType": "error", "contentLength": 21, "featuresDetected": ["技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": false}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "FAILED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:57:59.169Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "31fdaefd-a0f2-4461-9ba4-fa9e4f6e0087"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753966673936, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966679168, "responseTime": 5232, "httpStatus": 200, "responseData": {"success": true, "sessionId": "31fdaefd-a0f2-4461-9ba4-fa9e4f6e0087", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T12:57:59.165Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:58:13.891Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "d3afef6e-0142-4f5b-9a51-7f46abbdd534"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753966680170, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966693891, "responseTime": 13721, "httpStatus": 200, "responseData": {"success": true, "sessionId": "d3afef6e-0142-4f5b-9a51-7f46abbdd534", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T12:58:13.886Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:58:22.188Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "dbd73a44-b3c0-49db-b22c-a3f24df34f7a"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753966694893, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966702188, "responseTime": 7295, "httpStatus": 200, "responseData": {"success": true, "sessionId": "dbd73a44-b3c0-49db-b22c-a3f24df34f7a", "response": {"type": "professional_decline", "content": "我了解您在Python开发方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "Python开发"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:58:22.185Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["了解您"], "responseType": "professional_decline"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:58:30.376Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "b6393ff0-62c1-4f78-acb9-c974a9eb3819"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753966703189, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966710376, "responseTime": 7187, "httpStatus": 200, "responseData": {"success": true, "sessionId": "b6393ff0-62c1-4f78-acb9-c974a9eb3819", "response": {"type": "professional_decline", "content": "我了解您在您提到的技术方向方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "您提到的技术方向"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:58:30.374Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["技术方向", "了解您"], "responseType": "professional_decline"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:58:37.672Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "10dd14d5-bd2b-4962-828a-c376befa16eb"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753966711377, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966717671, "responseTime": 6294, "httpStatus": 200, "responseData": {"success": true, "sessionId": "10dd14d5-bd2b-4962-828a-c376befa16eb", "response": {"type": "professional_decline", "content": "我了解您在您提到的技术方向方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "您提到的技术方向"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:58:37.666Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["技术方向", "了解您"], "responseType": "professional_decline"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:58:58.561Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "1d48e5b6-c103-432f-b36c-5844e92b1c8e"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753966718673, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753966738560, "responseTime": 19887, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1d48e5b6-c103-432f-b36c-5844e92b1c8e", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina，很高兴为您服务。确实是个适合聊职业发展的好天气呢！为了能为您精准推荐机会，能否先简单介绍一下：\n\n1. 您的技术栈或专业方向（如Java/Python/前端等）\n2. 当前职级和工作年限\n3. 期望的工作地点和薪资范围？\n\n这样我可以更有针对性地为您提供建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "greeting", "timestamp": "2025-07-31T12:58:58.558Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["您的技术", "技术栈"], "responseType": "first_ai_response"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:09.395Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "fa86f26c-2a97-47d8-b27b-1cbf7fd35f89"}, "startTime": 1753966739564, "endTime": 1753966749395, "responseTime": 9831, "httpStatus": 200, "responseData": {"success": true, "sessionId": "fa86f26c-2a97-47d8-b27b-1cbf7fd35f89", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T12:59:09.392Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:15.680Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "8c91b748-9d87-4663-ace2-f69d246b3f3a"}, "dimension": "technology", "startTime": 1753966749396, "category": "RECOMMENDATION_MATRIX", "endTime": 1753966755679, "responseTime": 6283, "httpStatus": 200, "responseData": {"success": true, "sessionId": "8c91b748-9d87-4663-ace2-f69d246b3f3a", "response": {"type": "professional_decline", "content": "我了解您在Java、Spring Boot、MySQL方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "Java、Spring Boot、MySQL"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:59:15.673Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:22.610Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "6bf0f7be-2235-4661-92b6-cdc565704ea8"}, "dimension": "experience", "startTime": 1753966756681, "category": "RECOMMENDATION_MATRIX", "endTime": 1753966762610, "responseTime": 5929, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6bf0f7be-2235-4661-92b6-cdc565704ea8", "response": {"type": "professional_decline", "content": "我了解您在开发方面有5年的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "开发"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:59:22.594Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:29.919Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "98c61ade-de3e-465b-945a-7fb158d7cea3"}, "dimension": "industry", "startTime": 1753966763611, "category": "RECOMMENDATION_MATRIX", "endTime": 1753966769918, "responseTime": 6307, "httpStatus": 200, "responseData": {"success": true, "sessionId": "98c61ade-de3e-465b-945a-7fb158d7cea3", "response": {"type": "professional_decline", "content": "我了解您在金融科技方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "金融科技"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:59:29.916Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:36.891Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "054e7396-7c1e-4069-a9b6-3c68804d5c81"}, "dimension": "location", "startTime": 1753966770920, "category": "RECOMMENDATION_MATRIX", "endTime": 1753966776890, "responseTime": 5970, "httpStatus": 200, "responseData": {"success": true, "sessionId": "054e7396-7c1e-4069-a9b6-3c68804d5c81", "response": {"type": "professional_decline", "content": "我了解您在您提到的技术方向方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "您提到的技术方向"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:59:36.888Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T12:59:52.912Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "424f37d7-0e11-4543-98ac-b18367b9e182"}, "startTime": 1753966777893, "endTime": 1753966792911, "responseTime": 15018, "httpStatus": 200, "responseData": {"success": true, "sessionId": "424f37d7-0e11-4543-98ac-b18367b9e182", "response": {"type": "first_ai_response", "content": "您好！很高兴您愿意了解市场机会。为了给您更有针对性的建议，能否先简单分享：\n\n1. 您目前的技术栈或主要技术方向是？\n2. 当前所在公司类型（如互联网/金融/传统IT等）？\n3. 目前的职级（如初级/中级/高级工程师等）？\n\n这些信息能帮助我更好地评估市场匹配度。您也可以随时告诉我您特别关注的方面。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T12:59:52.907Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:00:15.194Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "2bab52e5-900f-4e52-aa00-481463f781a1"}, "startTime": 1753966792912, "category": "CONVERSATION_TERMINATION", "endTime": 1753966815194, "responseTime": 22282, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2bab52e5-900f-4e52-aa00-481463f781a1", "response": {"type": "first_ai_response", "content": "我理解您可能暂时不需要更多信息。不过为了更好地为您匹配合适的职位机会，能否请您简单分享以下关键信息：\n1. 您的技术栈或专业方向\n2. 当前职级\n3. 期望薪资范围\n这样我可以更精准地为您筛选机会。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "greeting", "timestamp": "2025-07-31T13:00:15.190Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:00:50.548Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "c9bcf8e7-d5cf-4786-9a4d-c04849a7c307"}, "startTime": 1753966816195, "category": "CONVERSATION_TERMINATION", "endTime": 1753966850548, "responseTime": 34353, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c9bcf8e7-d5cf-4786-9a4d-c04849a7c307", "response": {"type": "ai_understanding", "content": "您好Katrina！我理解您可能对求职方向还不太确定。作为专业的AI招聘助手，我可以帮您梳理以下几个关键点：\n\n1. 您目前的技术背景是？（比如：算法/开发/数据分析等）\n2. 您更关注哪些技术领域？（比如：机器学习/NLP/CV等）\n3. 您期望的工作地点是？\n\n您可以从这些方面给我一些信息，我会根据您的背景提供更有针对性的建议。", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "不知道"}}, "intent": "unknown", "timestamp": "2025-07-31T13:00:50.542Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:01:08.111Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "9382469c-8c62-4df8-a1d8-eec68b4cfc5d"}, "startTime": 1753966851550, "category": "CONVERSATION_TERMINATION", "endTime": 1753966868110, "responseTime": 16560, "httpStatus": 200, "responseData": {"success": true, "sessionId": "9382469c-8c62-4df8-a1d8-eec68b4cfc5d", "response": {"type": "first_ai_response", "content": "我理解您目前可能对求职有些犹豫。不过为了能给您更有针对性的建议，能否先简单告诉我：\n\n1. 您目前的技术栈或主要工作方向是什么？\n2. 现在的职级和公司情况如何？\n\n这样我可以帮您分析市场机会，即使暂时不考虑换工作，也能了解当前行业情况。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T13:01:08.108Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:01:09.129Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "bc46a786-c6e2-44b8-bf15-ec8083c426bd"}, "startTime": 1753966869114, "category": "ERROR_HANDLING", "endTime": 1753966869128, "responseTime": 14, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:01:35.787Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "3d4d703a-1466-4e41-83f5-44372117ae29"}, "startTime": 1753966870130, "category": "ERROR_HANDLING", "endTime": 1753966895786, "responseTime": 25656, "httpStatus": 200, "responseData": {"success": true, "sessionId": "3d4d703a-1466-4e41-83f5-44372117ae29", "response": {"type": "ai_understanding", "content": "您好，我注意到您发送了一段重复的字母内容。作为专业的AI招聘助手，我可能需要您提供更多具体信息才能更好地帮助您。\n\n为了更好地为您服务，您能否告诉我：\n1. 您目前的技术方向或求职领域（如AI算法、Java开发等）\n2. 您的工作经验年限\n3. 您期望的工作地点\n\n这样我可以更有针对性地为您提供职业建议和帮助。期待您的进一步说明！", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"}}, "intent": "unknown", "timestamp": "2025-07-31T13:01:35.781Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:01:36.798Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "8135d83c-8650-4d36-a51b-f15ae33067be"}, "startTime": 1753966896788, "category": "ERROR_HANDLING", "endTime": 1753966896798, "responseTime": 10, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T13:01:36.796Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:01:53.119Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "ddf38080-4987-421b-a1d8-0f1ffb2cd3c1"}, "startTime": 1753966897800, "category": "ERROR_HANDLING", "endTime": 1753966913119, "responseTime": 15319, "httpStatus": 200, "responseData": {"success": true, "sessionId": "ddf38080-4987-421b-a1d8-0f1ffb2cd3c1", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina，很高兴为您服务。为了更好地为您推荐合适的职位，能否请您先分享以下信息：\n1. 您的技术栈或专业方向（如Java/Python/前端等）\n2. 当前职级和工作经验年限\n3. 期望的工作地点和薪资范围\n\n这些信息将帮助我为您提供更精准的职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T13:01:53.116Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753966633548", "timestamp": "2025-07-31T13:02:42.890Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753966914121, "endTime": 1753966962889, "responseTime": 48768, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}