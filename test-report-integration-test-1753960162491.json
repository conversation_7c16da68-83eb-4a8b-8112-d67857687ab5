{"testId": "integration-test-1753960162491", "startTime": "2025-07-31T11:09:22.491Z", "endTime": "2025-07-31T11:14:35.070Z", "totalTestTime": 312579, "statistics": {"totalTests": 25, "passedTests": 21, "failedTests": 4, "testSuccessRate": 84, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 26825.64864864865, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 26825.64864864865, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:09:22.528Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753960162494, "endTime": 1753960162527, "responseTime": 33, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T11:09:22.524Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:09:29.481Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "19832795-3784-4340-9c06-76096fd6fa8f"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753960162528, "category": "DUAL_MODEL_AI", "endTime": 1753960169480, "responseTime": 6952, "httpStatus": 200, "responseData": {"success": true, "sessionId": "19832795-3784-4340-9c06-76096fd6fa8f", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T11:09:29.476Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:09:34.958Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "6990f675-e477-44e1-87a0-23c023348981"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753960170480, "category": "DUAL_MODEL_AI", "endTime": 1753960174957, "responseTime": 4477, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6990f675-e477-44e1-87a0-23c023348981", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:09:34.953Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:09:50.683Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "0fc9ff59-a32d-4379-a991-974379544631"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753960175958, "category": "DUAL_MODEL_AI", "endTime": 1753960190682, "responseTime": 14724, "httpStatus": 200, "responseData": {"success": true, "sessionId": "0fc9ff59-a32d-4379-a991-974379544631", "response": {"type": "first_ai_response", "content": "您好，很高兴为您提供求职协助。为了更好地匹配机会，请您补充以下信息：\n1. 您具体专注的大模型技术方向（如训练/推理/应用开发等）\n2. 当前职级（如初级/中级/资深工程师等）\n3. 期望的工作地点\n\n（注意：我不会询问当前公司名称以保护您的隐私）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:09:50.677Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 124, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:10:05.939Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "2571dbc7-a8a1-4881-8b3c-0e0fe59818f1"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753960191684, "category": "DUAL_MODEL_AI", "endTime": 1753960205939, "responseTime": 14255, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2571dbc7-a8a1-4881-8b3c-0e0fe59818f1", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:10:05.934Z"}, "analysis": {"hasResponse": true, "responseType": "unknown_intent", "contentLength": 33, "featuresDetected": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": true}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:10:14.093Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "dc3f6ad6-df4d-4225-b663-818916906f01"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960206942, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960214092, "responseTime": 7150, "httpStatus": 200, "responseData": {"success": true, "sessionId": "dc3f6ad6-df4d-4225-b663-818916906f01", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:10:14.089Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:10:27.319Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "2bdae12e-52ec-4d33-8d5f-043c3c7f238b"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960215095, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960227319, "responseTime": 12224, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2bdae12e-52ec-4d33-8d5f-043c3c7f238b", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:10:27.316Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:10:42.075Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "eab4f1bb-8189-4bb5-bc45-be5b059f83f5"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960228321, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960242075, "responseTime": 13754, "httpStatus": 200, "responseData": {"success": true, "sessionId": "eab4f1bb-8189-4bb5-bc45-be5b059f83f5", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:10:42.069Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:10:54.320Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "2c50d30d-2d86-4d9b-b57a-07381b4822be"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960243077, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960254320, "responseTime": 11243, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2c50d30d-2d86-4d9b-b57a-07381b4822be", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:10:54.315Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:11:08.430Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "d9509c90-9319-4b0c-8459-234c7ac4649c"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753960255322, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960268430, "responseTime": 13108, "httpStatus": 200, "responseData": {"success": true, "sessionId": "d9509c90-9319-4b0c-8459-234c7ac4649c", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "SALARY_INQUIRY", "timestamp": "2025-07-31T11:11:08.426Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:11:20.411Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "d84adbd7-f669-4918-92d6-43e3fc90c405"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753960269432, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960280411, "responseTime": 10979, "httpStatus": 200, "responseData": {"success": true, "sessionId": "d84adbd7-f669-4918-92d6-43e3fc90c405", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "GREETING", "timestamp": "2025-07-31T11:11:20.409Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:11:25.403Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "2c365764-fd2c-475d-a432-1874aa7a733e"}, "startTime": 1753960281413, "endTime": 1753960285403, "responseTime": 3990, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2c365764-fd2c-475d-a432-1874aa7a733e", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:11:25.399Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:11:38.239Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "81ae9349-7093-4371-91ae-08e1b1d81886"}, "dimension": "technology", "startTime": 1753960285404, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960298239, "responseTime": 12835, "httpStatus": 200, "responseData": {"success": true, "sessionId": "81ae9349-7093-4371-91ae-08e1b1d81886", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "RECOMMENDATION_REQUEST", "timestamp": "2025-07-31T11:11:38.236Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:11:54.880Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "582efe37-e1e0-4228-871e-6e32004e4bee"}, "dimension": "experience", "startTime": 1753960299240, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960314880, "responseTime": 15640, "httpStatus": 200, "responseData": {"success": true, "sessionId": "582efe37-e1e0-4228-871e-6e32004e4bee", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:11:54.878Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:12:06.986Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "d098d23a-1410-47ae-b6db-3c85a75b1dc6"}, "dimension": "industry", "startTime": 1753960315882, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960326986, "responseTime": 11104, "httpStatus": 200, "responseData": {"success": true, "sessionId": "d098d23a-1410-47ae-b6db-3c85a75b1dc6", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:12:06.983Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:12:21.119Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "86b884a0-3862-4fdf-8e30-9d4afb78345e"}, "dimension": "location", "startTime": 1753960327986, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960341118, "responseTime": 13132, "httpStatus": 200, "responseData": {"success": true, "sessionId": "86b884a0-3862-4fdf-8e30-9d4afb78345e", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:12:21.113Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:12:35.533Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "465cbca3-18c7-4a8d-9152-4cef935c9db6"}, "startTime": 1753960342121, "endTime": 1753960355532, "responseTime": 13411, "httpStatus": 200, "responseData": {"success": true, "sessionId": "465cbca3-18c7-4a8d-9152-4cef935c9db6", "response": {"type": "first_ai_response", "content": "了解您的需求。为了更好地评估市场机会，能否请您先分享：  \n1. 您的技术栈或专业方向？  \n2. 当前职级（如初级/中级/高级/专家等）？  \n（注：无需透露公司名称或具体薪资，初步了解背景即可）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:12:35.530Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:12:46.340Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "30dee19e-57d0-4be7-b527-e77704c5aaf6"}, "startTime": 1753960355534, "category": "CONVERSATION_TERMINATION", "endTime": 1753960366340, "responseTime": 10806, "httpStatus": 200, "responseData": {"success": true, "sessionId": "30dee19e-57d0-4be7-b527-e77704c5aaf6", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:12:46.336Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:12:57.510Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "a34c7a03-3470-4a79-bd2a-f62e491dcdde"}, "startTime": 1753960367342, "category": "CONVERSATION_TERMINATION", "endTime": 1753960377509, "responseTime": 10167, "httpStatus": 200, "responseData": {"success": true, "sessionId": "a34c7a03-3470-4a79-bd2a-f62e491dcdde", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:12:57.506Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:13:13.193Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "79fec2b0-ca9c-484e-b5b4-c8002cedb777"}, "startTime": 1753960378511, "category": "CONVERSATION_TERMINATION", "endTime": 1753960393193, "responseTime": 14682, "httpStatus": 200, "responseData": {"success": true, "sessionId": "79fec2b0-ca9c-484e-b5b4-c8002cedb777", "response": {"type": "first_ai_response", "content": "我理解您现在的想法。不过为了后续有合适机会时能第一时间联系您，能否简单告诉我：  \n\n1. 您目前的技术栈或专业方向是什么？  \n2. 现在的职级（如初级/中级/高级/专家等）？  \n\n这样我可以帮您留意匹配的机会。（无需立即回复，随时欢迎补充信息）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:13:13.190Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:13:14.210Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "95f112be-a9d0-44e1-9951-9cc5e5bbd335"}, "startTime": 1753960394195, "category": "ERROR_HANDLING", "endTime": 1753960394209, "responseTime": 14, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:13:24.412Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "bcb55f1c-1110-4d7e-9ad6-3a22d1370a90"}, "startTime": 1753960395210, "category": "ERROR_HANDLING", "endTime": 1753960404412, "responseTime": 9202, "httpStatus": 200, "responseData": {"success": true, "sessionId": "bcb55f1c-1110-4d7e-9ad6-3a22d1370a90", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:13:24.409Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:13:25.426Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "f355f2be-ea4a-4886-b4c7-f7ee68280382"}, "startTime": 1753960405413, "category": "ERROR_HANDLING", "endTime": 1753960405425, "responseTime": 12, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T11:13:25.423Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:13:38.569Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "df8c89f6-6604-433a-87a8-14fddd2003e1"}, "startTime": 1753960406427, "category": "ERROR_HANDLING", "endTime": 1753960418569, "responseTime": 12142, "httpStatus": 200, "responseData": {"success": true, "sessionId": "df8c89f6-6604-433a-87a8-14fddd2003e1", "response": {"type": "first_ai_response", "content": "您好，很高兴为您提供求职帮助。为了更好地了解您的需求，能否请您提供以下信息：\n1. 您的技术栈或专业方向是什么？\n2. 目前的工作年限和职级？\n3. 期望的工作地点？\n\n这些信息将帮助我更好地为您服务。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:13:38.566Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960162491", "timestamp": "2025-07-31T11:14:35.070Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753960419572, "endTime": 1753960475069, "responseTime": 55497, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}