{"testId": "integration-test-1753959106308", "startTime": "2025-07-31T10:51:46.308Z", "endTime": "2025-07-31T10:55:21.608Z", "totalTestTime": 215300, "statistics": {"totalTests": 25, "passedTests": 17, "failedTests": 8, "testSuccessRate": 68, "totalRequests": 124, "successfulRequests": 20, "apiSuccessRate": 16.129032258064516, "avgResponseTime": 1725.25, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 16.129032258064516, "apiSuccessRatePassed": false, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 1725.25, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:51:46.329Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753959106311, "endTime": 1753959106328, "responseTime": 17, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T10:51:46.326Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:51:54.376Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "433da486-92c0-4efe-8727-1e2f09c74cf7"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753959106329, "category": "DUAL_MODEL_AI", "endTime": 1753959114374, "responseTime": 8045, "httpStatus": 200, "responseData": {"success": true, "sessionId": "433da486-92c0-4efe-8727-1e2f09c74cf7", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T10:51:54.370Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<10秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:52:00.934Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "419bf9f8-2b9a-4dba-a23c-e6e40ddd4ad1"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753959114578, "category": "DUAL_MODEL_AI", "endTime": 1753959120933, "responseTime": 6355, "httpStatus": 200, "responseData": {"success": true, "sessionId": "419bf9f8-2b9a-4dba-a23c-e6e40ddd4ad1", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T10:52:00.929Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<10秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:52:15.166Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "c7ee66e0-8192-4a85-853f-38fc5a55a954"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753959121135, "category": "DUAL_MODEL_AI", "endTime": 1753959135165, "responseTime": 14030, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c7ee66e0-8192-4a85-853f-38fc5a55a954", "response": {"type": "first_ai_response", "content": "感谢您关注大模型方向。为了更好地帮您匹配合适的机会，能否请您分享以下信息：\n1. 您具体擅长的技术栈或研究方向（如NLP、多模态等）\n2. 当前职级和工作年限\n3. 期望的工作地点和薪资范围\n\n这些信息将帮助我为您提供更精准的建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T10:52:15.160Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 116, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<10秒", "passed": false}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:52:31.028Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "7141cef7-0fe5-4138-8d73-635c2e89f702"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753959135367, "category": "DUAL_MODEL_AI", "endTime": 1753959151027, "responseTime": 15660, "httpStatus": 200, "responseData": {"success": true, "sessionId": "7141cef7-0fe5-4138-8d73-635c2e89f702", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T10:52:31.023Z"}, "analysis": {"hasResponse": true, "responseType": "unknown_intent", "contentLength": 33, "featuresDetected": [], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<10秒", "passed": false}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": false}, {"check": "功能检测: 技术栈识别", "passed": false}, {"check": "功能检测: 薪资分析", "passed": false}, {"check": "功能检测: 地理位置", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:52:37.613Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "6922fa63-8edb-4967-b2e3-5742391c6831"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753959151231, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959157612, "responseTime": 6381, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6922fa63-8edb-4967-b2e3-5742391c6831", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T10:52:37.609Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:52:50.349Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "9c26b794-4c5a-4d0e-981d-e60c9798cbfd"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753959157813, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959170348, "responseTime": 12535, "httpStatus": 200, "responseData": {"success": true, "sessionId": "9c26b794-4c5a-4d0e-981d-e60c9798cbfd", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T10:52:50.347Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:02.568Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "5d3658f8-c69a-4a96-9a45-5f1a32515e26"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753959170550, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959182567, "responseTime": 12017, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5d3658f8-c69a-4a96-9a45-5f1a32515e26", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T10:53:02.562Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:13.856Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "b35a642d-31db-426d-abef-5b528c5eccb8"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753959182769, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959193855, "responseTime": 11086, "httpStatus": 200, "responseData": {"success": true, "sessionId": "b35a642d-31db-426d-abef-5b528c5eccb8", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T10:53:13.853Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:23.994Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "dcf33bed-b362-46ac-b8b0-f4c5f4531a37"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753959194058, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959203994, "responseTime": 9936, "httpStatus": 200, "responseData": {"success": true, "sessionId": "dcf33bed-b362-46ac-b8b0-f4c5f4531a37", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "SALARY_INQUIRY", "timestamp": "2025-07-31T10:53:23.993Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:36.151Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "995a0f6f-d459-49bc-b719-4d03b2342006"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753959204196, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753959216151, "responseTime": 11955, "httpStatus": 200, "responseData": {"success": true, "sessionId": "995a0f6f-d459-49bc-b719-4d03b2342006", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "GREETING", "timestamp": "2025-07-31T10:53:36.145Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:42.176Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "2a685f60-6b41-4dd0-827d-0a41af3ffc7b"}, "startTime": 1753959216354, "endTime": 1753959222176, "responseTime": 5822, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2a685f60-6b41-4dd0-827d-0a41af3ffc7b", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T10:53:42.174Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:53:54.532Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "668a5431-58f0-4456-8004-2f5cf95a5f57"}, "dimension": "technology", "startTime": 1753959222177, "category": "RECOMMENDATION_MATRIX", "endTime": 1753959234532, "responseTime": 12355, "httpStatus": 200, "responseData": {"success": true, "sessionId": "668a5431-58f0-4456-8004-2f5cf95a5f57", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "RECOMMENDATION_REQUEST", "timestamp": "2025-07-31T10:53:54.528Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:54:05.267Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "2c4030ea-449a-4eae-84d5-19c31d4720cf"}, "dimension": "experience", "startTime": 1753959234733, "category": "RECOMMENDATION_MATRIX", "endTime": 1753959245267, "responseTime": 10534, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2c4030ea-449a-4eae-84d5-19c31d4720cf", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T10:54:05.264Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:54:18.348Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "14fcc625-395b-4771-b42e-a66ababb6431"}, "dimension": "industry", "startTime": 1753959245468, "category": "RECOMMENDATION_MATRIX", "endTime": 1753959258347, "responseTime": 12879, "httpStatus": 200, "responseData": {"success": true, "sessionId": "14fcc625-395b-4771-b42e-a66ababb6431", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T10:54:18.342Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:54:29.368Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "1372616a-e1d8-4b4c-94af-06b968a71881"}, "dimension": "location", "startTime": 1753959258549, "category": "RECOMMENDATION_MATRIX", "endTime": 1753959269368, "responseTime": 10819, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1372616a-e1d8-4b4c-94af-06b968a71881", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T10:54:29.365Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:54:43.534Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "5a6a1a44-24b9-49a6-9f1c-6cd3eb25edf2"}, "startTime": 1753959269569, "endTime": 1753959283533, "responseTime": 13964, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5a6a1a44-24b9-49a6-9f1c-6cd3eb25edf2", "response": {"type": "first_ai_response", "content": "明白了，为了更好地帮您了解市场情况，我需要先了解一些基本信息：\n\n1. 您目前主要从事哪个技术方向或使用哪些技术栈？\n2. 您当前的职级是？（如初级/中级/高级工程师等）\n3. 您更关注哪些地区的工作机会？\n\n这些信息能帮助我为您提供更有参考价值的市场分析。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T10:54:43.531Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:54:55.251Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "f9303b20-c4f6-4757-ac01-b985c043f0d9"}, "startTime": 1753959283535, "category": "CONVERSATION_TERMINATION", "endTime": 1753959295251, "responseTime": 11716, "httpStatus": 200, "responseData": {"success": true, "sessionId": "f9303b20-c4f6-4757-ac01-b985c043f0d9", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T10:54:55.246Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:06.675Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "a0323bce-ae72-44a7-b2b6-41ba31828cf4"}, "startTime": 1753959295453, "category": "CONVERSATION_TERMINATION", "endTime": 1753959306675, "responseTime": 11222, "httpStatus": 200, "responseData": {"success": true, "sessionId": "a0323bce-ae72-44a7-b2b6-41ba31828cf4", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T10:55:06.672Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:20.512Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "54cfee31-e337-44b0-ad67-b723d251b4e4"}, "startTime": 1753959306876, "category": "CONVERSATION_TERMINATION", "endTime": 1753959320511, "responseTime": 13635, "httpStatus": 200, "responseData": {"success": true, "sessionId": "54cfee31-e337-44b0-ad67-b723d251b4e4", "response": {"type": "first_ai_response", "content": "我理解您目前可能没有强烈的求职意愿。不过为了帮您更好地评估未来的职业机会，能否先分享一些基本信息？比如：  \n\n1. 您目前的技术栈或专业方向是什么？  \n2. 当前的职级（如初级/中级/高级/专家等）？  \n\n这样我可以帮您了解市场情况，供未来参考。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T10:55:20.507Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:20.725Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "be922f54-0840-4386-bc8d-a8ce8ea1b3db"}, "startTime": 1753959320715, "category": "ERROR_HANDLING", "endTime": 1753959320725, "responseTime": 10, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:20.935Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "60821ac2-c0f8-4040-b83c-50ec9c7363b1"}, "startTime": 1753959320927, "category": "ERROR_HANDLING", "endTime": 1753959320934, "responseTime": 7, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:21.142Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "c9f56f3a-fb0f-4f5f-8145-baecd1c00114"}, "startTime": 1753959321136, "category": "ERROR_HANDLING", "endTime": 1753959321141, "responseTime": 5, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:21.352Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "5ad1853e-6843-4f81-8af1-b7a02c7d38b8"}, "startTime": 1753959321343, "category": "ERROR_HANDLING", "endTime": 1753959321352, "responseTime": 9, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753959106308", "timestamp": "2025-07-31T10:55:21.608Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753959321553, "endTime": 1753959321608, "responseTime": 55, "totalRequests": 100, "successfulRequests": 0, "failedRequests": 100, "successRate": 0, "status": "FAILED"}]}