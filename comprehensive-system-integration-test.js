/**
 * 完整系统集成测试
 *
 * 测试范围：
 * - 双模型AI调用测试
 * - 信息收集三种触发条件测试
 * - 第三方推荐功能测试
 * - 4x4推荐矩阵测试
 * - 被动推荐功能测试
 * - 对话终止逻辑测试
 * - 高并发压力测试（100并发）
 * - 容错机制测试
 *
 * 交付标准：
 * - 所有API调用成功率>95%
 * - 平均响应时间<3秒
 */

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");

class ComprehensiveSystemIntegrationTest {
  constructor() {
    this.baseUrl = "http://localhost:6789";
    this.testResults = [];
    this.startTime = Date.now();
    this.testId = `integration-test-${Date.now()}`;

    // 测试配置
    this.config = {
      timeout: 30000,
      concurrentCount: 100,
      successRateThreshold: 95,
      avgResponseTimeThreshold: 3000,
      maxRetries: 0,
    };

    // 统计数据
    this.stats = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalRequests: 0,
      successfulRequests: 0,
      totalResponseTime: 0,
      errors: [],
    };
  }

  /**
   * 执行完整的系统集成测试
   */
  async executeCompleteIntegrationTest() {
    console.log("🚀 开始执行完整系统集成测试");
    console.log(`测试ID: ${this.testId}`);
    console.log(`开始时间: ${new Date().toISOString()}`);
    console.log("=".repeat(80));

    try {
      // 1. 系统健康检查
      await this.testSystemHealth();

      // 2. 双模型AI调用测试
      await this.testDualModelAICalls();

      // 3. 信息收集三种触发条件测试
      await this.testInfoCollectionTriggers();

      // 4. 第三方推荐功能测试
      await this.testThirdPartyRecommendations();

      // 5. 4x4推荐矩阵测试
      await this.testRecommendationMatrix();

      // 6. 被动推荐功能测试
      await this.testPassiveRecommendations();

      // 7. 对话终止逻辑测试
      await this.testConversationTermination();

      // 8. 容错机制测试
      await this.testErrorHandlingMechanisms();

      // 9. 高并发压力测试（100并发）
      await this.testHighConcurrency();

      // 10. 生成测试报告
      this.generateTestReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.stats.errors.push({
        type: "CRITICAL_FAILURE",
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 测试1: 系统健康检查
   */
  async testSystemHealth() {
    console.log("\n📊 测试1: 系统健康检查");
    console.log("-".repeat(50));

    const testCase = {
      name: "系统健康检查",
      category: "SYSTEM_HEALTH",
      startTime: Date.now(),
    };

    try {
      const response = await this.makeRequest("GET", "/health");

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "PASSED";
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证健康检查响应
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        {
          check: "包含status字段",
          passed: response.data && "status" in response.data,
        },
        {
          check: "status为healthy",
          passed: response.data.status === "healthy",
        },
        { check: "响应时间<1000ms", passed: testCase.responseTime < 1000 },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log("✅ 系统健康检查通过");
        this.stats.passedTests++;
      } else {
        console.log("❌ 系统健康检查失败");
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }

      this.logTestResult(testCase);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log("❌ 系统健康检查异常:", error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
      this.logTestResult(testCase);
    }

    this.stats.totalTests++;
  }

  /**
   * 测试2: 双模型AI调用测试
   */
  async testDualModelAICalls() {
    console.log("\n🤖 测试2: 双模型AI调用测试");
    console.log("-".repeat(50));

    const testCases = [
      {
        name: "DeepSeek模型调用 - 对话生成",
        input: {
          message: "你好，我想找一份Java开发的工作",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        expectedModel: "deepseek",
        expectedFeatures: ["对话生成", "信息引导"],
      },
      {
        name: "DeepSeek模型调用 - 技术咨询",
        input: {
          message: "我是Python算法工程师，有5年经验，期望薪资40k",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        expectedModel: "deepseek",
        expectedFeatures: ["技术理解", "经验分析"],
      },
      {
        name: "AI模型切换逻辑测试",
        input: {
          message: "推荐一些适合我的职位",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        expectedModel: "deepseek",
        expectedFeatures: ["推荐引导", "信息收集"],
      },
      {
        name: "复杂对话场景测试",
        input: {
          message: "我在阿里巴巴工作过3年，现在想转到字节跳动做大模型相关工作",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        expectedModel: "deepseek",
        expectedFeatures: ["公司理解", "技术方向", "职业规划"],
      },
    ];

    for (const testCase of testCases) {
      await this.executeDualModelTest(testCase);
      await this.delay(200); // 避免请求过于频繁
    }
  }

  /**
   * 执行双模型测试
   */
  async executeDualModelTest(testCase) {
    testCase.startTime = Date.now();
    testCase.category = "DUAL_MODEL_AI";

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 分析AI响应
      const analysis = this.analyzeAIResponse(
        response.data,
        testCase.expectedFeatures
      );
      testCase.analysis = analysis;

      // 验证断言
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "包含AI响应",
          passed: !!(response.data.response && response.data.response.content),
        },
        { check: "响应时间<10秒", passed: testCase.responseTime < 10000 },
        {
          check: "内容长度>10字符",
          passed: response.data.response?.content?.length > 10,
        },
      ];

      // 检查预期功能
      testCase.expectedFeatures.forEach((feature) => {
        assertions.push({
          check: `功能检测: ${feature}`,
          passed: analysis.featuresDetected.includes(feature),
        });
      });

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log(`✅ ${testCase.name} 通过`);
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log(`❌ ${testCase.name} 失败`);
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(
        `📄 AI响应: ${response.data.response?.content?.substring(0, 100)}...`
      );
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ ${testCase.name} 异常:`, error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 分析AI响应
   */
  analyzeAIResponse(responseData, expectedFeatures) {
    const analysis = {
      hasResponse: !!responseData.response,
      responseType: responseData.response?.type || "unknown",
      contentLength: responseData.response?.content?.length || 0,
      featuresDetected: [],
    };

    if (responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测功能特征
      expectedFeatures.forEach((feature) => {
        let detected = false;
        switch (feature) {
          case "对话生成":
            detected =
              content.length > 20 &&
              (content.includes("您") ||
                content.includes("请") ||
                content.includes("能否"));
            break;
          case "信息引导":
            detected =
              content.includes("信息") ||
              content.includes("告诉") ||
              content.includes("提供");
            break;
          case "技术理解":
            detected =
              content.includes("技术") ||
              content.includes("python") ||
              content.includes("算法");
            break;
          case "经验分析":
            detected =
              content.includes("经验") ||
              content.includes("年") ||
              content.includes("工作");
            break;
          case "推荐引导":
            detected =
              content.includes("推荐") ||
              content.includes("职位") ||
              content.includes("机会");
            break;
          case "信息收集":
            detected =
              content.includes("信息") ||
              content.includes("了解") ||
              content.includes("补充");
            break;
          case "公司理解":
            detected =
              content.includes("公司") ||
              content.includes("阿里") ||
              content.includes("字节");
            break;
          case "技术方向":
            detected =
              content.includes("技术") ||
              content.includes("方向") ||
              content.includes("大模型");
            break;
          case "职业规划":
            detected =
              content.includes("职业") ||
              content.includes("规划") ||
              content.includes("发展");
            break;
        }

        if (detected) {
          analysis.featuresDetected.push(feature);
        }
      });
    }

    return analysis;
  }

  /**
   * 发起HTTP请求
   */
  async makeRequest(method, endpoint, data = null) {
    this.stats.totalRequests++;
    const startTime = Date.now();

    try {
      let response;
      const config = {
        timeout: this.config.timeout,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      };

      if (method === "GET") {
        response = await axios.get(`${this.baseUrl}${endpoint}`, config);
      } else if (method === "POST") {
        response = await axios.post(`${this.baseUrl}${endpoint}`, data, config);
      }

      const responseTime = Date.now() - startTime;
      this.stats.totalResponseTime += responseTime;
      this.stats.successfulRequests++;

      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.stats.totalResponseTime += responseTime;
      throw error;
    }
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 测试3: 信息收集三种触发条件测试
   */
  async testInfoCollectionTriggers() {
    console.log("\n📋 测试3: 信息收集三种触发条件测试");
    console.log("-".repeat(50));

    const testCases = [
      // 主动触发测试
      {
        name: "主动触发 - 直接职位询问",
        input: {
          message: "有什么职位推荐吗",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "active",
        expectedTrigger: true,
      },
      {
        name: "主动触发 - 工作机会咨询",
        input: {
          message: "我想了解一下工作机会",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "active",
        expectedTrigger: true,
      },

      // 关键词触发测试
      {
        name: "关键词触发 - 技术栈提及",
        input: {
          message: "我是Python开发工程师",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "keyword",
        expectedTrigger: true,
      },
      {
        name: "关键词触发 - 公司经历",
        input: {
          message: "我在腾讯工作过2年",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "keyword",
        expectedTrigger: true,
      },

      // 上下文触发测试
      {
        name: "上下文触发 - 薪资期望",
        input: {
          message: "期望薪资在30k左右",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "context",
        expectedTrigger: true,
      },

      // 非触发测试
      {
        name: "非触发 - 无关内容",
        input: {
          message: "今天天气真好",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        triggerType: "none",
        expectedTrigger: false,
      },
    ];

    for (const testCase of testCases) {
      await this.executeInfoCollectionTest(testCase);
      await this.delay(200);
    }
  }

  /**
   * 执行信息收集触发测试
   */
  async executeInfoCollectionTest(testCase) {
    testCase.startTime = Date.now();
    testCase.category = "INFO_COLLECTION_TRIGGER";

    try {
      console.log(`\n🔍 执行触发测试: ${testCase.name}`);
      console.log(`📝 触发类型: ${testCase.triggerType}`);
      console.log(`🎯 预期触发: ${testCase.expectedTrigger}`);

      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 分析触发情况
      const triggerAnalysis = this.analyzeTriggerResponse(response.data);
      testCase.triggerAnalysis = triggerAnalysis;

      // 验证断言
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "触发正确性",
          passed: triggerAnalysis.triggered === testCase.expectedTrigger,
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log(`✅ ${testCase.name} 通过`);
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log(`❌ ${testCase.name} 失败`);
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      console.log(`❌ ${testCase.name} 异常:`, error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 分析触发响应
   */
  analyzeTriggerResponse(responseData) {
    const analysis = {
      triggered: false,
      triggerKeywords: [],
      responseType: responseData.response?.type || "unknown",
    };

    if (responseData.success && responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测信息收集触发关键词
      const triggerKeywords = [
        "请告诉我",
        "能否告诉我",
        "您的技术",
        "工作经验",
        "期望薪资",
        "技术栈",
        "技术方向",
        "所在公司",
        "项目经历",
        "离职原因",
        "期望公司",
        "补充信息",
        "了解您",
        "为了更好",
        "能否请您",
        "分享以下",
      ];

      triggerKeywords.forEach((keyword) => {
        if (content.includes(keyword.toLowerCase())) {
          analysis.triggerKeywords.push(keyword);
          analysis.triggered = true;
        }
      });
    }

    return analysis;
  }

  /**
   * 测试4: 第三方推荐功能测试
   */
  async testThirdPartyRecommendations() {
    console.log("\n🔗 测试4: 第三方推荐功能测试");
    console.log("-".repeat(50));

    // 由于第三方推荐功能可能需要特定的触发条件，我们测试相关的推荐逻辑
    const testCase = {
      name: "第三方推荐功能测试",
      category: "THIRD_PARTY_RECOMMENDATION",
      input: {
        message: "我想了解一些外部的职位推荐",
        userEmail: "<EMAIL>",
        sessionId: uuidv4(),
      },
      startTime: Date.now(),
    };

    try {
      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证推荐功能响应
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "包含推荐内容",
          passed: !!(response.data.response && response.data.response.content),
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log("✅ 第三方推荐功能测试通过");
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log("❌ 第三方推荐功能测试失败");
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      console.log("❌ 第三方推荐功能测试异常:", error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 测试5: 4x4推荐矩阵测试
   */
  async testRecommendationMatrix() {
    console.log("\n📊 测试5: 4x4推荐矩阵测试");
    console.log("-".repeat(50));

    // 测试推荐矩阵的不同维度
    const matrixTestCases = [
      {
        name: "技术栈维度推荐",
        input: {
          message: "我精通Java、Spring Boot、MySQL，有什么推荐",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        dimension: "technology",
      },
      {
        name: "经验级别维度推荐",
        input: {
          message: "我有5年开发经验，想找高级工程师职位",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        dimension: "experience",
      },
      {
        name: "行业领域维度推荐",
        input: {
          message: "我想在金融科技领域发展",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        dimension: "industry",
      },
      {
        name: "地理位置维度推荐",
        input: {
          message: "我希望在北京或上海工作",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
        dimension: "location",
      },
    ];

    for (const testCase of matrixTestCases) {
      await this.executeMatrixTest(testCase);
      await this.delay(200);
    }
  }

  /**
   * 执行矩阵测试
   */
  async executeMatrixTest(testCase) {
    testCase.startTime = Date.now();
    testCase.category = "RECOMMENDATION_MATRIX";

    try {
      console.log(`\n🔍 执行矩阵测试: ${testCase.name}`);

      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证矩阵推荐
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "包含推荐逻辑",
          passed: !!(response.data.response && response.data.response.content),
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log(`✅ ${testCase.name} 通过`);
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log(`❌ ${testCase.name} 失败`);
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      console.log(`❌ ${testCase.name} 异常:`, error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 测试6: 被动推荐功能测试
   */
  async testPassiveRecommendations() {
    console.log("\n🎯 测试6: 被动推荐功能测试");
    console.log("-".repeat(50));

    const testCase = {
      name: "被动推荐功能测试",
      category: "PASSIVE_RECOMMENDATION",
      input: {
        message: "我目前还没有明确的求职意向，只是了解一下市场",
        userEmail: "<EMAIL>",
        sessionId: uuidv4(),
      },
      startTime: Date.now(),
    };

    try {
      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证被动推荐
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "提供市场信息",
          passed: !!(response.data.response && response.data.response.content),
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log("✅ 被动推荐功能测试通过");
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log("❌ 被动推荐功能测试失败");
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      console.log("❌ 被动推荐功能测试异常:", error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 测试7: 对话终止逻辑测试
   */
  async testConversationTermination() {
    console.log("\n🔚 测试7: 对话终止逻辑测试");
    console.log("-".repeat(50));

    const terminationTestCases = [
      {
        name: "用户主动终止对话",
        input: {
          message: "谢谢，我不需要更多信息了",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
      {
        name: "重复无效输入终止",
        input: {
          message: "不知道",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
      {
        name: "明确拒绝服务",
        input: {
          message: "我不想找工作了",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
    ];

    for (const testCase of terminationTestCases) {
      await this.executeTerminationTest(testCase);
      await this.delay(200);
    }
  }

  /**
   * 执行终止逻辑测试
   */
  async executeTerminationTest(testCase) {
    testCase.startTime = Date.now();
    testCase.category = "CONVERSATION_TERMINATION";

    try {
      console.log(`\n🔍 执行终止测试: ${testCase.name}`);

      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证终止逻辑
      const assertions = [
        { check: "HTTP状态200", passed: response.status === 200 },
        { check: "响应成功", passed: response.data.success === true },
        {
          check: "适当处理终止",
          passed: !!(response.data.response && response.data.response.content),
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log(`✅ ${testCase.name} 通过`);
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log(`❌ ${testCase.name} 失败`);
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      console.log(`❌ ${testCase.name} 异常:`, error.message);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 测试8: 容错机制测试
   */
  async testErrorHandlingMechanisms() {
    console.log("\n🛡️ 测试8: 容错机制测试");
    console.log("-".repeat(50));

    const errorTestCases = [
      {
        name: "空消息处理",
        input: {
          message: "",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
      {
        name: "超长消息处理",
        input: {
          message: "A".repeat(10000),
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
      {
        name: "特殊字符处理",
        input: {
          message: "!@#$%^&*()_+{}|:\"<>?[]\\;',./",
          userEmail: "<EMAIL>",
          sessionId: uuidv4(),
        },
      },
      {
        name: "无效邮箱格式",
        input: {
          message: "我想找工作",
          userEmail: "invalid-email",
          sessionId: uuidv4(),
        },
      },
    ];

    for (const testCase of errorTestCases) {
      await this.executeErrorHandlingTest(testCase);
      await this.delay(200);
    }
  }

  /**
   * 执行容错机制测试
   */
  async executeErrorHandlingTest(testCase) {
    testCase.startTime = Date.now();
    testCase.category = "ERROR_HANDLING";

    try {
      console.log(`\n🔍 执行容错测试: ${testCase.name}`);

      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.responseData = response.data;

      // 验证容错处理
      const assertions = [
        {
          check: "HTTP状态200或400",
          passed: response.status === 200 || response.status === 400,
        },
        { check: "系统未崩溃", passed: true }, // 如果能到这里说明系统没崩溃
        {
          check: "有错误处理响应",
          passed: !!(response.data.response || response.data.error),
        },
      ];

      testCase.assertions = assertions;
      const allPassed = assertions.every((a) => a.passed);

      if (allPassed) {
        console.log(`✅ ${testCase.name} 通过`);
        testCase.status = "PASSED";
        this.stats.passedTests++;
      } else {
        console.log(`❌ ${testCase.name} 失败`);
        testCase.status = "FAILED";
        this.stats.failedTests++;
      }
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "PASSED"; // 对于容错测试，捕获到错误也算通过
      testCase.error = error.message;

      console.log(`✅ ${testCase.name} 通过 (正确捕获错误: ${error.message})`);
      this.stats.passedTests++;
    }

    this.stats.totalTests++;
    this.logTestResult(testCase);
  }

  /**
   * 测试9: 高并发压力测试（100并发）
   */
  async testHighConcurrency() {
    console.log("\n🚀 测试4: 高并发压力测试（100并发）");
    console.log("-".repeat(50));

    const concurrentRequests = [];
    const testStartTime = Date.now();

    // 创建100个并发请求
    for (let i = 0; i < this.config.concurrentCount; i++) {
      const testCase = {
        name: `并发请求-${i + 1}`,
        category: "HIGH_CONCURRENCY",
        input: {
          message: `我是第${i + 1}个用户，想找工作`,
          userEmail: `concurrent-user-${i + 1}@example.com`,
          sessionId: uuidv4(),
        },
        requestIndex: i + 1,
        startTime: Date.now(),
      };

      const requestPromise = this.executeConcurrentRequest(testCase);
      concurrentRequests.push(requestPromise);
    }

    console.log(`🔄 发起${this.config.concurrentCount}个并发请求...`);

    try {
      // 等待所有请求完成
      const results = await Promise.allSettled(concurrentRequests);
      const testEndTime = Date.now();
      const totalTestTime = testEndTime - testStartTime;

      // 统计结果
      const successCount = results.filter(
        (r) => r.status === "fulfilled" && r.value.status === "PASSED"
      ).length;
      const failureCount = results.filter(
        (r) => r.status === "rejected" || r.value.status === "FAILED"
      ).length;
      const successRate = (successCount / this.config.concurrentCount) * 100;

      console.log(`📊 并发测试结果:`);
      console.log(`   总请求数: ${this.config.concurrentCount}`);
      console.log(`   成功数: ${successCount}`);
      console.log(`   失败数: ${failureCount}`);
      console.log(`   成功率: ${successRate.toFixed(2)}%`);
      console.log(`   总耗时: ${totalTestTime}ms`);

      // 记录并发测试结果
      const concurrencyTestCase = {
        name: "高并发压力测试",
        category: "HIGH_CONCURRENCY_SUMMARY",
        startTime: testStartTime,
        endTime: testEndTime,
        responseTime: totalTestTime,
        totalRequests: this.config.concurrentCount,
        successfulRequests: successCount,
        failedRequests: failureCount,
        successRate: successRate,
        status:
          successRate >= this.config.successRateThreshold ? "PASSED" : "FAILED",
      };

      if (concurrencyTestCase.status === "PASSED") {
        console.log("✅ 高并发测试通过");
        this.stats.passedTests++;
      } else {
        console.log("❌ 高并发测试失败");
        this.stats.failedTests++;
      }

      this.stats.totalTests++;
      this.logTestResult(concurrencyTestCase);
    } catch (error) {
      console.error("❌ 高并发测试异常:", error);
      this.stats.failedTests++;
      this.stats.errors.push(error);
    }
  }

  /**
   * 执行单个并发请求
   */
  async executeConcurrentRequest(testCase) {
    try {
      const response = await this.makeRequest(
        "POST",
        "/api/chat/message",
        testCase.input
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.status =
        response.status === 200 && response.data.success ? "PASSED" : "FAILED";

      return testCase;
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILED";
      testCase.error = error.message;

      return testCase;
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    console.log("\n📋 生成测试报告");
    console.log("=".repeat(80));

    const endTime = Date.now();
    const totalTestTime = endTime - this.startTime;
    const avgResponseTime =
      this.stats.totalResponseTime / this.stats.totalRequests;
    const successRate =
      (this.stats.successfulRequests / this.stats.totalRequests) * 100;

    const report = {
      testId: this.testId,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      totalTestTime: totalTestTime,
      statistics: {
        totalTests: this.stats.totalTests,
        passedTests: this.stats.passedTests,
        failedTests: this.stats.failedTests,
        testSuccessRate: (this.stats.passedTests / this.stats.totalTests) * 100,
        totalRequests: this.stats.totalRequests,
        successfulRequests: this.stats.successfulRequests,
        apiSuccessRate: successRate,
        avgResponseTime: avgResponseTime,
        errors: this.stats.errors.length,
      },
      compliance: {
        apiSuccessRateTarget: this.config.successRateThreshold,
        apiSuccessRateActual: successRate,
        apiSuccessRatePassed: successRate >= this.config.successRateThreshold,
        avgResponseTimeTarget: this.config.avgResponseTimeThreshold,
        avgResponseTimeActual: avgResponseTime,
        avgResponseTimePassed:
          avgResponseTime <= this.config.avgResponseTimeThreshold,
      },
      testResults: this.testResults,
    };

    // 输出报告
    console.log("📊 测试统计:");
    console.log(`   总测试数: ${report.statistics.totalTests}`);
    console.log(`   通过测试: ${report.statistics.passedTests}`);
    console.log(`   失败测试: ${report.statistics.failedTests}`);
    console.log(
      `   测试成功率: ${report.statistics.testSuccessRate.toFixed(2)}%`
    );
    console.log(`   总请求数: ${report.statistics.totalRequests}`);
    console.log(
      `   API成功率: ${report.statistics.apiSuccessRate.toFixed(2)}%`
    );
    console.log(
      `   平均响应时间: ${report.statistics.avgResponseTime.toFixed(2)}ms`
    );

    console.log("\n🎯 合规性检查:");
    console.log(
      `   API成功率要求: >${
        this.config.successRateThreshold
      }% | 实际: ${report.compliance.apiSuccessRateActual.toFixed(2)}% | ${
        report.compliance.apiSuccessRatePassed ? "✅ 通过" : "❌ 失败"
      }`
    );
    console.log(
      `   平均响应时间要求: <${
        this.config.avgResponseTimeThreshold
      }ms | 实际: ${report.compliance.avgResponseTimeActual.toFixed(2)}ms | ${
        report.compliance.avgResponseTimePassed ? "✅ 通过" : "❌ 失败"
      }`
    );

    // 保存报告到文件
    const reportFileName = `test-report-${this.testId}.json`;
    fs.writeFileSync(reportFileName, JSON.stringify(report, null, 2));
    console.log(`\n📄 测试报告已保存: ${reportFileName}`);

    // 最终结论
    const overallPassed =
      report.compliance.apiSuccessRatePassed &&
      report.compliance.avgResponseTimePassed &&
      report.statistics.testSuccessRate >= 90;

    console.log("\n🏁 最终结论:");
    if (overallPassed) {
      console.log("✅ 系统集成测试通过，系统可以部署上线");
    } else {
      console.log("❌ 系统集成测试失败，需要修复问题后重新测试");
    }

    return report;
  }

  /**
   * 记录测试结果
   */
  logTestResult(testCase) {
    this.testResults.push({
      testId: this.testId,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 执行测试
if (require.main === module) {
  const tester = new ComprehensiveSystemIntegrationTest();
  tester.executeCompleteIntegrationTest().catch(console.error);
}

module.exports = ComprehensiveSystemIntegrationTest;
