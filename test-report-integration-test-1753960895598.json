{"testId": "integration-test-1753960895598", "startTime": "2025-07-31T11:21:35.598Z", "endTime": "2025-07-31T11:26:31.566Z", "totalTestTime": 295968, "statistics": {"totalTests": 25, "passedTests": 22, "failedTests": 3, "testSuccessRate": 88, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 26121.45945945946, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 26121.45945945946, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:21:35.629Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753960895606, "endTime": 1753960895629, "responseTime": 23, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T11:21:35.626Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:21:40.290Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "6f7cecb0-765a-42a6-9d83-7511c20fd1ed"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753960895630, "category": "DUAL_MODEL_AI", "endTime": 1753960900288, "responseTime": 4658, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6f7cecb0-765a-42a6-9d83-7511c20fd1ed", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T11:21:40.284Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:21:45.559Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "8bee00d1-1a74-47ce-b620-9f04c2ab7d10"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753960901291, "category": "DUAL_MODEL_AI", "endTime": 1753960905558, "responseTime": 4267, "httpStatus": 200, "responseData": {"success": true, "sessionId": "8bee00d1-1a74-47ce-b620-9f04c2ab7d10", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:21:45.554Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:21:57.964Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "81f38044-8262-4105-95ca-8ffc6b4db8ae"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753960906561, "category": "DUAL_MODEL_AI", "endTime": 1753960917963, "responseTime": 11402, "httpStatus": 200, "responseData": {"success": true, "sessionId": "81f38044-8262-4105-95ca-8ffc6b4db8ae", "response": {"type": "first_ai_response", "content": "感谢您关注大模型方向。为了更好地为您匹配合适的机会，我需要了解以下信息：\n1. 您主要使用哪些技术栈或框架？\n2. 目前所在公司的职级是？\n3. 期望的工作地点是？\n\n（注意：不询问具体公司名称，避免违反限制）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:21:57.960Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 105, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:22:11.490Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "201b4f67-78a7-4e0b-a9f8-8b8e0e8346cd"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753960918965, "category": "DUAL_MODEL_AI", "endTime": 1753960931490, "responseTime": 12525, "httpStatus": 200, "responseData": {"success": true, "sessionId": "201b4f67-78a7-4e0b-a9f8-8b8e0e8346cd", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:22:11.487Z"}, "analysis": {"hasResponse": true, "responseType": "unknown_intent", "contentLength": 33, "featuresDetected": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": true}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:22:16.034Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "44486085-4b8e-42c0-9d23-fd33ddf2f21f"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960932495, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960936032, "responseTime": 3537, "httpStatus": 200, "responseData": {"success": true, "sessionId": "44486085-4b8e-42c0-9d23-fd33ddf2f21f", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:22:16.030Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:22:29.308Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "aa6a9cf3-40b3-43cb-b02e-3aa1ff8a2112"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960937035, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960949307, "responseTime": 12272, "httpStatus": 200, "responseData": {"success": true, "sessionId": "aa6a9cf3-40b3-43cb-b02e-3aa1ff8a2112", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:22:29.302Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:22:42.153Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "1f888d05-f80b-4828-8009-919be99aee00"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960950310, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960962153, "responseTime": 11843, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1f888d05-f80b-4828-8009-919be99aee00", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:22:42.146Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:22:55.064Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "29a2cb7c-31e7-4630-b97d-5f0879aa7f65"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960963155, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960975064, "responseTime": 11909, "httpStatus": 200, "responseData": {"success": true, "sessionId": "29a2cb7c-31e7-4630-b97d-5f0879aa7f65", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:22:55.059Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:23:08.808Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "a9890fbe-e068-4616-87e2-dca095054517"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753960976066, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960988808, "responseTime": 12742, "httpStatus": 200, "responseData": {"success": true, "sessionId": "a9890fbe-e068-4616-87e2-dca095054517", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "SALARY_INQUIRY", "timestamp": "2025-07-31T11:23:08.801Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:23:21.428Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "83d609b9-1d95-41cb-b705-5abb3c0240c5"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753960989810, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753961001427, "responseTime": 11617, "httpStatus": 200, "responseData": {"success": true, "sessionId": "83d609b9-1d95-41cb-b705-5abb3c0240c5", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "GREETING", "timestamp": "2025-07-31T11:23:21.422Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:23:26.079Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "bdb9cd39-460f-4a5e-8b5b-506ff631012b"}, "startTime": 1753961002429, "endTime": 1753961006079, "responseTime": 3650, "httpStatus": 200, "responseData": {"success": true, "sessionId": "bdb9cd39-460f-4a5e-8b5b-506ff631012b", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:23:26.076Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:23:37.240Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "c35048d7-cabc-47a5-ac4c-439fc74e57b5"}, "dimension": "technology", "startTime": 1753961006080, "category": "RECOMMENDATION_MATRIX", "endTime": 1753961017239, "responseTime": 11159, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c35048d7-cabc-47a5-ac4c-439fc74e57b5", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "RECOMMENDATION_REQUEST", "timestamp": "2025-07-31T11:23:37.236Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:23:52.293Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "90096863-1477-47dc-8bf2-561de4333154"}, "dimension": "experience", "startTime": 1753961018242, "category": "RECOMMENDATION_MATRIX", "endTime": 1753961032292, "responseTime": 14050, "httpStatus": 200, "responseData": {"success": true, "sessionId": "90096863-1477-47dc-8bf2-561de4333154", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:23:52.286Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:24:06.158Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "61fa5894-2717-48c3-bf50-566fd3beccf9"}, "dimension": "industry", "startTime": 1753961033294, "category": "RECOMMENDATION_MATRIX", "endTime": 1753961046157, "responseTime": 12863, "httpStatus": 200, "responseData": {"success": true, "sessionId": "61fa5894-2717-48c3-bf50-566fd3beccf9", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:24:06.152Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:24:20.791Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "5cfd4071-7c53-4991-bab9-203ae5f15ad3"}, "dimension": "location", "startTime": 1753961047159, "category": "RECOMMENDATION_MATRIX", "endTime": 1753961060791, "responseTime": 13632, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5cfd4071-7c53-4991-bab9-203ae5f15ad3", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:24:20.788Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:24:35.048Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "3f237bc1-3333-4803-97a7-2caab653f40b"}, "startTime": 1753961061794, "endTime": 1753961075048, "responseTime": 13254, "httpStatus": 200, "responseData": {"success": true, "sessionId": "3f237bc1-3333-4803-97a7-2caab653f40b", "response": {"type": "first_ai_response", "content": "了解您的需求。为了更好地评估市场机会，能否请您分享以下信息：\n1. 您的技术专长或主要工作领域\n2. 当前职级(如初级/中级/高级/架构师等)\n3. 期望的工作地点\n\n这些信息将帮助我为您提供更有价值的市场参考。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:24:35.044Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:24:45.858Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "c7f4df99-0e00-42a0-a597-10a621e87d74"}, "startTime": 1753961075049, "category": "CONVERSATION_TERMINATION", "endTime": 1753961085858, "responseTime": 10809, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c7f4df99-0e00-42a0-a597-10a621e87d74", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:24:45.856Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:24:57.571Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "59761884-fce5-4d6e-85dc-8e1feda749a3"}, "startTime": 1753961086859, "category": "CONVERSATION_TERMINATION", "endTime": 1753961097570, "responseTime": 10711, "httpStatus": 200, "responseData": {"success": true, "sessionId": "59761884-fce5-4d6e-85dc-8e1feda749a3", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:24:57.565Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:25:11.577Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "90344065-3b46-4832-bc89-1937695cf02f"}, "startTime": 1753961098572, "category": "CONVERSATION_TERMINATION", "endTime": 1753961111576, "responseTime": 13004, "httpStatus": 200, "responseData": {"success": true, "sessionId": "90344065-3b46-4832-bc89-1937695cf02f", "response": {"type": "first_ai_response", "content": "我理解您现在的想法。不过为了未来可能的机会，建议我们可以先完善您的职业档案。能否简单分享一下：\n\n1. 您目前的技术方向或专业领域是什么？\n2. 现在的职级是？(如初级/中级/高级工程师等)\n\n这样当您准备好时，我们可以更快地为您匹配合适的机会。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:25:11.571Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:25:12.599Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "42a4d812-5770-492e-b8e9-3548e2d2ae5e"}, "startTime": 1753961112579, "category": "ERROR_HANDLING", "endTime": 1753961112599, "responseTime": 20, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:25:24.587Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "f18e8beb-b219-4e57-a16b-e1070de8038c"}, "startTime": 1753961113601, "category": "ERROR_HANDLING", "endTime": 1753961124586, "responseTime": 10985, "httpStatus": 200, "responseData": {"success": true, "sessionId": "f18e8beb-b219-4e57-a16b-e1070de8038c", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:25:24.584Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:25:25.595Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "1b11dad5-81a7-487e-bce9-75bbf40734a7"}, "startTime": 1753961125587, "category": "ERROR_HANDLING", "endTime": 1753961125595, "responseTime": 8, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T11:25:25.593Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:25:42.198Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "445f0fdd-5efe-4e6e-9237-36873ec1ddf3"}, "startTime": 1753961126596, "category": "ERROR_HANDLING", "endTime": 1753961142198, "responseTime": 15602, "httpStatus": 200, "responseData": {"success": true, "sessionId": "445f0fdd-5efe-4e6e-9237-36873ec1ddf3", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina。为了帮您匹配合适的机会，请先分享以下信息：  \n1. 您的技术栈或专业方向  \n2. 当前职级（如初级/中级/高级/专家等）  \n3. 期望的工作地点  \n4. 薪资期望范围（如：涨幅百分比或区间）  \n\n（请避免透露具体公司名称或薪资数字）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:25:42.194Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960895598", "timestamp": "2025-07-31T11:26:31.566Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753961143200, "endTime": 1753961191565, "responseTime": 48365, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}