{"testId": "integration-test-1753960045547", "startTime": "2025-07-31T11:07:25.547Z", "endTime": "2025-07-31T11:07:54.803Z", "totalTestTime": 29256, "statistics": {"totalTests": 25, "passedTests": 5, "failedTests": 20, "testSuccessRate": 20, "totalRequests": 74, "successfulRequests": 1, "apiSuccessRate": 1.3513513513513513, "avgResponseTime": 8.067567567567568, "errors": 19}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 1.3513513513513513, "apiSuccessRatePassed": false, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 8.067567567567568, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:25.582Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753960045561, "endTime": 1753960045582, "responseTime": 21, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T11:07:25.579Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:25.586Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "22dc08cf-d112-4140-bc3a-25ace11baeb5"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753960045583, "category": "DUAL_MODEL_AI", "endTime": 1753960045586, "responseTime": 3, "status": "FAILED", "error": "Request failed with status code 403", "errorStack": "AxiosError: Request failed with status code 403\n    at settle (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:468:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:26.596Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "2e175944-d476-43db-a2e3-3778d871167d"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753960046589, "category": "DUAL_MODEL_AI", "endTime": 1753960046596, "responseTime": 7, "status": "FAILED", "error": "Request failed with status code 403", "errorStack": "AxiosError: Request failed with status code 403\n    at settle (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:468:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:27.603Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "5d2d5885-bfbd-4f92-8289-4c167752809d"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753960047597, "category": "DUAL_MODEL_AI", "endTime": 1753960047603, "responseTime": 6, "status": "FAILED", "error": "Request failed with status code 403", "errorStack": "AxiosError: Request failed with status code 403\n    at settle (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:468:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:28.610Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "5e91218e-93d2-41d3-ac3c-98bddd264f81"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753960048605, "category": "DUAL_MODEL_AI", "endTime": 1753960048609, "responseTime": 4, "status": "FAILED", "error": "Request failed with status code 403", "errorStack": "AxiosError: Request failed with status code 403\n    at settle (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:536:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:468:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:29.617Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "63d41812-64a4-4a30-9dc3-76b629ca6715"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960049612, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960049617, "responseTime": 5, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:30.624Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "33f17b35-d942-488e-950c-6652c22ee4fb"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960050617, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960050624, "responseTime": 7, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:31.628Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "e53e92f0-af9d-4355-8ba7-db46af220ac4"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960051626, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960051628, "responseTime": 2, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:32.632Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "1609e051-ba5b-4bee-b1c6-18627b4a8da9"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960052629, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960052632, "responseTime": 3, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:33.640Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "a5e21a1c-a0dc-47f4-a795-ec1fa99ce291"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753960053634, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960053640, "responseTime": 6, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:34.650Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "ae33644f-0376-4f25-88f6-fa78352a5a70"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753960054641, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960054650, "responseTime": 9, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:35.661Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "70948d09-b71d-44cf-a7b3-01c391a6854f"}, "startTime": 1753960055653, "endTime": 1753960055661, "responseTime": 8, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:35.668Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "45eb9b34-50f4-4107-85ba-64b8caf41773"}, "dimension": "technology", "startTime": 1753960055662, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960055668, "responseTime": 6, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:36.672Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "cb1d7a78-940b-41b4-a950-f0ff24fba22a"}, "dimension": "experience", "startTime": 1753960056669, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960056672, "responseTime": 3, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:37.680Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "b1b3a4cf-d2c1-4b71-98ed-f13ab6b57763"}, "dimension": "industry", "startTime": 1753960057673, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960057680, "responseTime": 7, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:38.688Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "d9438038-bbcc-412a-9126-98e71b0ecbea"}, "dimension": "location", "startTime": 1753960058682, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960058688, "responseTime": 6, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:39.696Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "f3cf67e1-12a1-477c-9db7-487ade14d22d"}, "startTime": 1753960059691, "endTime": 1753960059696, "responseTime": 5, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:39.701Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "a25986ee-3270-4fc5-8947-b890baced3e1"}, "startTime": 1753960059696, "category": "CONVERSATION_TERMINATION", "endTime": 1753960059701, "responseTime": 5, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:40.708Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "6a9f704f-ee00-4703-b66b-0d4bcb2c6c3b"}, "startTime": 1753960060702, "category": "CONVERSATION_TERMINATION", "endTime": 1753960060708, "responseTime": 6, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:41.712Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "4af2a85f-aaeb-4e97-8eb3-9047a6951bdc"}, "startTime": 1753960061709, "category": "CONVERSATION_TERMINATION", "endTime": 1753960061712, "responseTime": 3, "status": "FAILED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:42.719Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "eff4327c-0a2f-4502-9dae-7284f080ba7a"}, "startTime": 1753960062714, "category": "ERROR_HANDLING", "endTime": 1753960062719, "responseTime": 5, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:43.725Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "3bf326ea-2b4c-4308-8edd-56854651ec14"}, "startTime": 1753960063720, "category": "ERROR_HANDLING", "endTime": 1753960063725, "responseTime": 5, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:44.741Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "931a738f-98e9-4ef7-843f-77adcbedff9c"}, "startTime": 1753960064727, "category": "ERROR_HANDLING", "endTime": 1753960064741, "responseTime": 14, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:45.749Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "fbfd1eb3-de7e-4ae3-8580-1c75bf3d2fea"}, "startTime": 1753960065742, "category": "ERROR_HANDLING", "endTime": 1753960065749, "responseTime": 7, "status": "PASSED", "error": "Request failed with status code 403"}, {"testId": "integration-test-1753960045547", "timestamp": "2025-07-31T11:07:54.802Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753960066750, "endTime": 1753960074802, "responseTime": 8052, "totalRequests": 50, "successfulRequests": 0, "failedRequests": 50, "successRate": 0, "status": "FAILED"}]}