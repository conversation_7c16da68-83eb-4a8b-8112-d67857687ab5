/**
 * 直接测试Qwen API的真实能力
 */

require('dotenv').config();
const axios = require('axios');

async function testQwenDirect() {
  console.log('🔍 直接测试Qwen API能力');
  
  const testCases = [
    {
      name: "简单信息提取",
      prompt: `请从以下用户消息中提取信息，以JSON格式返回：

用户消息: "我是Java开发工程师，有3年经验"

请提取：技术方向、工作经验

返回格式：{"技术方向": "...", "工作经验": "..."}`,
      expected: {
        技术方向: "Java开发",
        工作经验: "3年"
      }
    },
    {
      name: "公司转换信息",
      prompt: `请从以下用户消息中提取信息，以JSON格式返回：

用户消息: "我在阿里巴巴工作过2年，现在想转到字节跳动"

请提取：当前公司、工作经验、目标公司

返回格式：{"当前公司": "...", "工作经验": "...", "目标公司": "..."}`,
      expected: {
        当前公司: "阿里巴巴",
        工作经验: "2年", 
        目标公司: "字节跳动"
      }
    },
    {
      name: "复杂信息提取",
      prompt: `请从以下用户消息中提取信息，以JSON格式返回：

用户消息: "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作"

请提取：技术方向、工作经验、期望薪资、工作地点

返回格式：{"技术方向": "...", "工作经验": "...", "期望薪资": "...", "工作地点": "..."}`,
      expected: {
        技术方向: "Python算法",
        工作经验: "5年",
        期望薪资: "40k",
        工作地点: "北京"
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log(`📝 提示词: ${testCase.prompt.substring(0, 100)}...`);
    
    // 测试不同的temperature值
    const temperatures = [0.1, 0.3, 0.7];
    
    for (const temp of temperatures) {
      console.log(`\n🌡️ Temperature: ${temp}`);
      
      try {
        const requestBody = {
          model: "qwen-turbo",
          input: {
            messages: [
              {
                role: "user",
                content: testCase.prompt,
              },
            ],
          },
          parameters: {
            max_tokens: 300,
            temperature: temp,
            top_p: 0.8,
          },
        };

        const response = await axios.post(
          'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
          requestBody,
          {
            headers: {
              Authorization: `Bearer ${process.env.QWEN_API_KEY}`,
              "Content-Type": "application/json",
            },
            timeout: 30000,
          }
        );

        if (response.data && response.data.output && response.data.output.text) {
          const content = response.data.output.text;
          console.log(`✅ 返回内容: ${content}`);
          
          // 尝试解析JSON
          try {
            const parsed = JSON.parse(content);
            console.log(`📊 解析结果:`, parsed);
            
            // 检查准确性
            let accuracy = 0;
            let total = 0;
            Object.entries(testCase.expected).forEach(([key, expectedValue]) => {
              total++;
              if (parsed[key] && parsed[key].includes(expectedValue)) {
                accuracy++;
                console.log(`✅ ${key}: 正确 (${parsed[key]})`);
              } else {
                console.log(`❌ ${key}: 错误 (期望: ${expectedValue}, 实际: ${parsed[key]})`);
              }
            });
            
            console.log(`📊 准确率: ${accuracy}/${total} (${(accuracy/total*100).toFixed(1)}%)`);
            
          } catch (parseError) {
            console.log(`❌ JSON解析失败: ${parseError.message}`);
          }
          
        } else {
          console.log(`❌ 响应格式异常:`, response.data);
        }
        
      } catch (error) {
        console.log(`❌ API调用失败: ${error.message}`);
        if (error.response) {
          console.log(`❌ 响应状态: ${error.response.status}`);
          console.log(`❌ 响应数据:`, error.response.data);
        }
      }
      
      // 延迟避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// 执行测试
if (require.main === module) {
  testQwenDirect().catch(console.error);
}

module.exports = testQwenDirect;
