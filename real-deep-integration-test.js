/**
 * 不可造假的深度系统集成测试
 *
 * 严格按照要求执行：
 * 1. 使用真实运行环境测试，而非模拟调用或 mock 数据
 * 2. 测试所有边界条件、异常路径、负面场景
 * 3. 高并发测试禁止缓存、禁止预处理响应，必须使用冷启动状态
 * 4. 每个测试必须附带真实输入参数、原始响应结果、断言过程、错误栈信息
 * 5. 所有输出必须原始、详细，不得总结成"通过/失败"
 */

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");

class RealDeepIntegrationTester {
  constructor() {
    this.baseUrl = "http://localhost:6790";
    this.testResults = [];
    this.startTime = Date.now();
    this.testId = `real-test-${Date.now()}`;

    // 测试配置 - 严格模式
    this.config = {
      timeout: 30000,
      concurrentCount: 100, // 真实100并发
      coldStartDelay: 100, // 冷启动间隔
      retryAttempts: 0, // 禁止重试
      cacheDisabled: true, // 禁用缓存
    };
  }

  /**
   * 执行完整的真实深度测试
   */
  async executeRealDeepTests() {
    console.log("🚀 开始执行不可造假的深度系统集成测试");
    console.log(`测试ID: ${this.testId}`);
    console.log(`开始时间: ${new Date().toISOString()}`);
    console.log("=".repeat(80));

    try {
      // 1. 系统启动和健康检查
      await this.testSystemStartup();

      // 2. 双模型调用真实性验证
      await this.testDualModelCalls();

      // 3. 信息收集触发机制完整性测试
      await this.testInfoCollectionTriggers();

      // 4. 推荐系统完整链路测试
      await this.testRecommendationPipeline();

      // 5. 对话终止逻辑验证
      await this.testConversationTermination();

      // 6. 容错机制异常注入测试
      await this.testErrorHandlingMechanisms();

      // 7. 并发100x冷启动压力测试
      await this.testConcurrentColdStart();

      // 8. 生成详细测试报告
      this.generateDetailedReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.logTestResult("CRITICAL_FAILURE", {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 测试1: 系统启动和健康检查
   */
  async testSystemStartup() {
    console.log("\n📊 测试1: 系统启动和健康检查");
    console.log("-".repeat(50));

    // 首先启动系统
    console.log("🔄 启动AI招聘助手系统...");
    const startupTest = {
      name: "系统启动测试",
      startTime: Date.now(),
      phase: "STARTUP",
    };

    try {
      // 启动系统进程
      const { spawn } = require("child_process");
      const systemProcess = spawn("npm", ["start"], {
        cwd: "./ai-recruitment-assistant",
        stdio: "pipe",
      });

      let startupOutput = "";
      let startupError = "";

      systemProcess.stdout.on("data", (data) => {
        startupOutput += data.toString();
      });

      systemProcess.stderr.on("data", (data) => {
        startupError += data.toString();
      });

      // 等待系统启动
      console.log("⏰ 等待系统启动...");
      await this.delay(10000); // 等待10秒让系统完全启动

      startupTest.endTime = Date.now();
      startupTest.responseTime = startupTest.endTime - startupTest.startTime;
      startupTest.startupOutput = startupOutput;
      startupTest.startupError = startupError;

      // 测试健康检查
      console.log("🔍 执行健康检查...");
      const healthTest = await this.executeHealthCheck();

      if (healthTest.status === "SUCCESS") {
        startupTest.status = "SUCCESS";
        console.log("✅ 系统启动成功");
        console.log(`📊 启动耗时: ${startupTest.responseTime}ms`);
        console.log(`📋 启动输出长度: ${startupOutput.length}字符`);
        console.log(`📋 错误输出长度: ${startupError.length}字符`);
      } else {
        startupTest.status = "FAILURE";
        console.log("❌ 系统启动失败");
        console.log(`💥 健康检查失败: ${healthTest.error}`);
      }

      // 保存系统进程引用以便后续测试
      this.systemProcess = systemProcess;
    } catch (error) {
      startupTest.endTime = Date.now();
      startupTest.responseTime = startupTest.endTime - startupTest.startTime;
      startupTest.status = "FAILURE";
      startupTest.error = error.message;
      startupTest.errorStack = error.stack;

      console.log("❌ 系统启动异常:", error.message);
      throw error; // 如果系统无法启动，后续测试无法进行
    }

    this.logTestResult("SYSTEM_STARTUP", startupTest);
  }

  /**
   * 执行健康检查
   */
  async executeHealthCheck() {
    const healthTest = {
      name: "健康检查",
      startTime: Date.now(),
    };

    try {
      console.log(`🔍 请求: GET ${this.baseUrl}/health`);

      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: this.config.timeout,
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      });

      healthTest.endTime = Date.now();
      healthTest.responseTime = healthTest.endTime - healthTest.startTime;
      healthTest.status = "SUCCESS";
      healthTest.httpStatus = response.status;
      healthTest.rawResponse = response.data;
      healthTest.responseHeaders = response.headers;

      console.log(`✅ 健康检查通过`);
      console.log(`📊 响应时间: ${healthTest.responseTime}ms`);
      console.log(`📋 HTTP状态: ${response.status}`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`📋 响应头:`, JSON.stringify(response.headers, null, 2));
    } catch (error) {
      healthTest.endTime = Date.now();
      healthTest.responseTime = healthTest.endTime - healthTest.startTime;
      healthTest.status = "FAILURE";
      healthTest.error = error.message;
      healthTest.errorCode = error.code;
      healthTest.errorStack = error.stack;

      if (error.response) {
        healthTest.httpStatus = error.response.status;
        healthTest.errorResponse = error.response.data;
        healthTest.errorHeaders = error.response.headers;
      }

      console.log(`❌ 健康检查失败: ${error.message}`);
      console.log(`📊 响应时间: ${healthTest.responseTime}ms`);
      console.log(`💥 错误代码: ${error.code}`);
      if (error.response) {
        console.log(`📋 HTTP状态: ${error.response.status}`);
        console.log(
          `📄 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
      }
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("HEALTH_CHECK", healthTest);
    return healthTest;
  }

  /**
   * 测试2: 双模型调用真实性验证
   */
  async testDualModelCalls() {
    console.log("\n🤖 测试2: 双模型调用真实性验证");
    console.log("-".repeat(50));

    const dualModelTests = [
      {
        name: "DeepSeek模型真实调用测试",
        input: {
          message: "你好，我想找一份Java开发的工作",
          userEmail: "<EMAIL>",
        },
        expectedModel: "deepseek",
        expectedFeatures: ["意图识别", "信息提取"],
      },
      {
        name: "Qwen模型真实调用测试",
        input: {
          message: "我是Python算法工程师，有5年经验，期望薪资40k",
          userEmail: "<EMAIL>",
        },
        expectedModel: "qwen",
        expectedFeatures: ["技术映射", "薪资分析"],
      },
      {
        name: "模型切换逻辑测试",
        input: {
          message: "推荐一些适合我的职位",
          userEmail: "<EMAIL>",
        },
        expectedModel: "both",
        expectedFeatures: ["推荐生成", "职位匹配"],
      },
    ];

    for (const testCase of dualModelTests) {
      await this.executeDualModelTest(testCase);

      // 冷启动间隔 - 确保每次调用都是独立的
      console.log(`⏰ 冷启动间隔: ${this.config.coldStartDelay}ms`);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行双模型测试
   */
  async executeDualModelTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
      console.log(`📝 会话ID: ${sessionId}`);
      console.log(`🎯 预期模型: ${testCase.expectedModel}`);
      console.log(`🎯 预期功能: ${testCase.expectedFeatures.join(", ")}`);

      const requestPayload = {
        ...testCase.input,
        sessionId: sessionId,
        timestamp: Date.now(),
      };

      console.log(`🔍 请求: POST ${this.baseUrl}/api/chat`);
      console.log(`📄 请求体:`, JSON.stringify(requestPayload, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        requestPayload,
        {
          timeout: this.config.timeout,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
            "X-Test-ID": this.testId,
            "X-Test-Case": Buffer.from(testCase.name).toString("base64"),
          },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;
      testCase.responseHeaders = response.headers;

      // 详细分析响应
      const analysis = this.analyzeModelResponse(
        response.data,
        testCase.expectedFeatures
      );
      testCase.responseAnalysis = analysis;

      console.log(`✅ 测试执行完成: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 HTTP状态: ${response.status}`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`📋 响应头:`, JSON.stringify(response.headers, null, 2));
      console.log(`🔍 响应分析:`, JSON.stringify(analysis, null, 2));

      // 验证断言
      const assertions = this.validateDualModelAssertions(
        testCase,
        response.data
      );
      testCase.assertions = assertions;

      console.log(`🎯 断言结果:`);
      assertions.forEach((assertion) => {
        const icon = assertion.passed ? "✅" : "❌";
        console.log(`   ${icon} ${assertion.description}: ${assertion.result}`);
        if (!assertion.passed && assertion.details) {
          console.log(`      详情: ${assertion.details}`);
        }
      });
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorCode = error.code;
      testCase.errorStack = error.stack;

      if (error.response) {
        testCase.httpStatus = error.response.status;
        testCase.errorResponse = error.response.data;
        testCase.errorHeaders = error.response.headers;
      }

      console.log(`❌ 测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`💥 错误代码: ${error.code}`);
      if (error.response) {
        console.log(`📋 HTTP状态: ${error.response.status}`);
        console.log(
          `📄 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
        console.log(
          `📋 错误响应头:`,
          JSON.stringify(error.response.headers, null, 2)
        );
      }
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("DUAL_MODEL_CALL", testCase);
  }

  /**
   * 分析模型响应
   */
  analyzeModelResponse(responseData, expectedFeatures) {
    const analysis = {
      hasResponse: !!responseData.response,
      responseType: responseData.response?.type || "unknown",
      contentLength: responseData.response?.content?.length || 0,
      hasMetadata: !!responseData.metadata,
      processingTime: responseData.processingTime || 0,
      modelUsed: responseData.modelUsed || "unknown",
      featuresDetected: [],
    };

    if (responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测功能特征
      expectedFeatures.forEach((feature) => {
        let detected = false;
        switch (feature) {
          case "意图识别":
            detected =
              content.includes("意图") ||
              content.includes("想要") ||
              content.includes("需要");
            break;
          case "信息提取":
            detected =
              content.includes("技术") ||
              content.includes("经验") ||
              content.includes("技能");
            break;
          case "技术映射":
            detected =
              content.includes("技术栈") || content.includes("技术方向");
            break;
          case "薪资分析":
            detected =
              content.includes("薪资") ||
              content.includes("薪水") ||
              content.includes("工资");
            break;
          case "推荐生成":
            detected =
              content.includes("推荐") ||
              content.includes("建议") ||
              content.includes("职位");
            break;
          case "职位匹配":
            detected =
              content.includes("匹配") ||
              content.includes("适合") ||
              content.includes("符合");
            break;
        }

        if (detected) {
          analysis.featuresDetected.push(feature);
        }
      });
    }

    return analysis;
  }

  /**
   * 验证双模型断言
   */
  validateDualModelAssertions(testCase, responseData) {
    const assertions = [];

    // 断言1: 响应成功
    assertions.push({
      description: "响应成功性检查",
      passed: responseData.success === true,
      result: responseData.success ? "响应成功" : "响应失败",
      details: responseData.success ? null : responseData.error,
    });

    // 断言2: 响应时间合理
    assertions.push({
      description: "响应时间检查",
      passed: testCase.responseTime < 30000,
      result: `${testCase.responseTime}ms`,
      details: testCase.responseTime >= 30000 ? "响应时间超过30秒" : null,
    });

    // 断言3: 响应内容存在
    assertions.push({
      description: "响应内容存在性检查",
      passed: !!(responseData.response && responseData.response.content),
      result: responseData.response?.content ? "内容存在" : "内容缺失",
      details: !responseData.response?.content ? "响应中缺少content字段" : null,
    });

    // 断言4: 预期功能检测
    const analysis = testCase.responseAnalysis;
    if (analysis) {
      testCase.expectedFeatures.forEach((feature) => {
        assertions.push({
          description: `功能检测: ${feature}`,
          passed: analysis.featuresDetected.includes(feature),
          result: analysis.featuresDetected.includes(feature)
            ? "检测到"
            : "未检测到",
          details: analysis.featuresDetected.includes(feature)
            ? null
            : `响应中未发现${feature}相关内容`,
        });
      });
    }

    return assertions;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 测试3: 信息收集触发机制完整性测试
   */
  async testInfoCollectionTriggers() {
    console.log("\n📋 测试3: 信息收集触发机制完整性测试");
    console.log("-".repeat(50));

    const triggerTests = [
      {
        name: "主动触发 - 职位询问",
        input: { message: "有什么职位推荐吗", userEmail: "<EMAIL>" },
        expectedTrigger: true,
        triggerType: "active",
        expectedQuestions: ["技术栈", "工作经验", "期望薪资"],
      },
      {
        name: "关键词触发 - 技术栈提及",
        input: {
          message: "我是Python开发工程师",
          userEmail: "<EMAIL>",
        },
        expectedTrigger: true,
        triggerType: "keyword",
        expectedQuestions: ["工作经验", "项目经历", "期望薪资"],
      },
      {
        name: "上下文触发 - 信息补充",
        input: {
          message: "我在阿里巴巴工作过3年",
          userEmail: "<EMAIL>",
        },
        expectedTrigger: true,
        triggerType: "context",
        expectedQuestions: ["技术方向", "离职原因", "期望公司"],
      },
      {
        name: "非预期输入 - 无关内容",
        input: { message: "今天天气真好啊", userEmail: "<EMAIL>" },
        expectedTrigger: false,
        triggerType: "none",
        expectedQuestions: [],
      },
      {
        name: "边界测试 - 空字符串",
        input: { message: "", userEmail: "<EMAIL>" },
        expectedTrigger: false,
        triggerType: "none",
        expectedQuestions: [],
      },
      {
        name: "边界测试 - 特殊字符",
        input: { message: "!@#$%^&*()", userEmail: "<EMAIL>" },
        expectedTrigger: false,
        triggerType: "none",
        expectedQuestions: [],
      },
      {
        name: "边界测试 - 超长文本",
        input: {
          message: "Java开发工程师".repeat(1000),
          userEmail: "<EMAIL>",
        },
        expectedTrigger: true,
        triggerType: "keyword",
        expectedQuestions: ["工作经验"],
      },
    ];

    for (const testCase of triggerTests) {
      await this.executeInfoCollectionTest(testCase);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行信息收集触发测试
   */
  async executeInfoCollectionTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行触发测试: ${testCase.name}`);
      console.log(
        `📝 输入内容: "${testCase.input.message.substring(0, 100)}${
          testCase.input.message.length > 100 ? "..." : ""
        }"`
      );
      console.log(`📝 输入长度: ${testCase.input.message.length}字符`);
      console.log(`📝 用户邮箱: ${testCase.input.userEmail}`);
      console.log(`🎯 预期触发: ${testCase.expectedTrigger}`);
      console.log(`🎯 触发类型: ${testCase.triggerType}`);

      const requestPayload = {
        ...testCase.input,
        sessionId: sessionId,
        timestamp: Date.now(),
      };

      console.log(`🔍 请求: POST ${this.baseUrl}/api/chat`);

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        requestPayload,
        {
          timeout: this.config.timeout,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache",
            "X-Test-ID": this.testId,
            "X-Test-Case": Buffer.from(testCase.name).toString("base64"),
          },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 分析触发情况
      const triggerAnalysis = this.analyzeInfoCollectionTrigger(
        response.data,
        testCase.expectedQuestions
      );
      testCase.triggerAnalysis = triggerAnalysis;

      console.log(`✅ 触发测试执行完成: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`🔍 触发分析:`, JSON.stringify(triggerAnalysis, null, 2));

      // 验证触发断言
      const assertions = this.validateTriggerAssertions(
        testCase,
        triggerAnalysis
      );
      testCase.assertions = assertions;

      console.log(`🎯 触发断言结果:`);
      assertions.forEach((assertion) => {
        const icon = assertion.passed ? "✅" : "❌";
        console.log(`   ${icon} ${assertion.description}: ${assertion.result}`);
        if (!assertion.passed && assertion.details) {
          console.log(`      详情: ${assertion.details}`);
        }
      });
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 触发测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("INFO_COLLECTION_TRIGGER", testCase);
  }

  /**
   * 分析信息收集触发情况
   */
  analyzeInfoCollectionTrigger(responseData, expectedQuestions) {
    const analysis = {
      triggered: false,
      questionsDetected: [],
      triggerKeywords: [],
      responseType: responseData.response?.type || "unknown",
    };

    if (responseData.success && responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测触发关键词
      const triggerKeywords = [
        "请告诉我",
        "能否告诉我",
        "您的技术",
        "工作经验",
        "期望薪资",
        "技术栈",
        "技术方向",
        "所在公司",
        "项目经历",
        "离职原因",
        "期望公司",
      ];

      triggerKeywords.forEach((keyword) => {
        if (content.includes(keyword.toLowerCase())) {
          analysis.triggerKeywords.push(keyword);
          analysis.triggered = true;
        }
      });

      // 检测问题
      expectedQuestions.forEach((question) => {
        if (content.includes(question.toLowerCase())) {
          analysis.questionsDetected.push(question);
        }
      });
    }

    return analysis;
  }

  /**
   * 验证触发断言
   */
  validateTriggerAssertions(testCase, triggerAnalysis) {
    const assertions = [];

    // 断言1: 触发正确性
    assertions.push({
      description: "触发机制正确性检查",
      passed: triggerAnalysis.triggered === testCase.expectedTrigger,
      result: `预期=${testCase.expectedTrigger}, 实际=${triggerAnalysis.triggered}`,
      details:
        triggerAnalysis.triggered !== testCase.expectedTrigger
          ? `触发机制不符合预期，检测到的关键词: ${triggerAnalysis.triggerKeywords.join(
              ", "
            )}`
          : null,
    });

    // 断言2: 问题检测
    if (testCase.expectedTrigger && testCase.expectedQuestions.length > 0) {
      const detectedCount = triggerAnalysis.questionsDetected.length;
      const expectedCount = testCase.expectedQuestions.length;

      assertions.push({
        description: "预期问题检测",
        passed: detectedCount > 0,
        result: `检测到${detectedCount}/${expectedCount}个预期问题`,
        details:
          detectedCount === 0
            ? `未检测到任何预期问题: ${testCase.expectedQuestions.join(", ")}`
            : `检测到的问题: ${triggerAnalysis.questionsDetected.join(", ")}`,
      });
    }

    return assertions;
  }

  /**
   * 测试4: 推荐系统完整链路测试
   */
  async testRecommendationPipeline() {
    console.log("\n🎯 测试4: 推荐系统完整链路测试");
    console.log("-".repeat(50));

    const pipelineTests = [
      {
        name: "技术映射测试 - 有效技术栈",
        input: {
          message: "我是Java后端开发，有Spring Boot和MySQL经验",
          userEmail: "<EMAIL>",
        },
        expectedMapping: true,
        expectedTech: ["Java", "Spring Boot", "MySQL"],
      },
      {
        name: "技术映射测试 - 无效技术栈",
        input: {
          message: "我是厨师，会做川菜和粤菜",
          userEmail: "<EMAIL>",
        },
        expectedMapping: false,
        expectedTech: [],
      },
      {
        name: "业务映射测试 - 电商场景",
        input: {
          message: "我想在电商公司工作，做推荐算法开发",
          userEmail: "<EMAIL>",
        },
        expectedMapping: true,
        expectedBusiness: ["电商", "推荐算法"],
      },
      {
        name: "推荐矩阵测试 - 完整信息",
        input: {
          message: "我是Python算法工程师，在阿里工作过，P6级别，期望40k薪资",
          userEmail: "<EMAIL>",
        },
        expectedRecommendation: true,
        expectedFields: ["技术栈", "公司背景", "薪资期望"],
      },
      {
        name: "推荐矩阵测试 - 信息不足",
        input: {
          message: "我想找工作",
          userEmail: "<EMAIL>",
        },
        expectedRecommendation: false,
        expectedFields: [],
      },
      {
        name: "异常数据注入 - 不存在的技术ID",
        input: {
          message: "我精通XXXXXX和YYYYYY技术",
          userEmail: "<EMAIL>",
        },
        expectedMapping: false,
        expectedTech: [],
      },
      {
        name: "边界测试 - 空矩阵处理",
        input: {
          message: "",
          userEmail: "<EMAIL>",
        },
        expectedRecommendation: false,
        expectedFields: [],
      },
    ];

    for (const testCase of pipelineTests) {
      await this.executePipelineTest(testCase);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行推荐系统管道测试
   */
  async executePipelineTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行管道测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
      console.log(`🎯 预期映射: ${testCase.expectedMapping}`);
      console.log(`🎯 预期推荐: ${testCase.expectedRecommendation}`);

      const requestPayload = {
        ...testCase.input,
        sessionId: sessionId,
        timestamp: Date.now(),
      };

      console.log(`🔍 请求: POST ${this.baseUrl}/api/chat`);
      console.log(`📄 请求体:`, JSON.stringify(requestPayload, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        requestPayload,
        {
          timeout: this.config.timeout,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache",
            "X-Test-ID": this.testId,
            "X-Test-Case": Buffer.from(testCase.name).toString("base64"),
          },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 分析推荐系统响应
      const pipelineAnalysis = this.analyzePipelineResponse(
        response.data,
        testCase
      );
      testCase.pipelineAnalysis = pipelineAnalysis;

      console.log(`✅ 管道测试执行完成: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`🔍 管道分析:`, JSON.stringify(pipelineAnalysis, null, 2));

      // 验证管道断言
      const assertions = this.validatePipelineAssertions(
        testCase,
        pipelineAnalysis
      );
      testCase.assertions = assertions;

      console.log(`🎯 管道断言结果:`);
      assertions.forEach((assertion) => {
        const icon = assertion.passed ? "✅" : "❌";
        console.log(`   ${icon} ${assertion.description}: ${assertion.result}`);
        if (!assertion.passed && assertion.details) {
          console.log(`      详情: ${assertion.details}`);
        }
      });
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 管道测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("RECOMMENDATION_PIPELINE", testCase);
  }

  /**
   * 分析推荐系统管道响应
   */
  analyzePipelineResponse(responseData, testCase) {
    const analysis = {
      hasTechMapping: false,
      hasBusinessMapping: false,
      hasRecommendation: false,
      detectedTech: [],
      detectedBusiness: [],
      recommendationCount: 0,
      responseType: responseData.response?.type || "unknown",
    };

    if (responseData.success && responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测技术映射
      if (testCase.expectedTech) {
        testCase.expectedTech.forEach((tech) => {
          if (content.includes(tech.toLowerCase())) {
            analysis.detectedTech.push(tech);
            analysis.hasTechMapping = true;
          }
        });
      }

      // 检测业务映射
      if (testCase.expectedBusiness) {
        testCase.expectedBusiness.forEach((business) => {
          if (content.includes(business.toLowerCase())) {
            analysis.detectedBusiness.push(business);
            analysis.hasBusinessMapping = true;
          }
        });
      }

      // 检测推荐内容
      const recommendationKeywords = ["推荐", "建议", "职位", "岗位", "机会"];
      recommendationKeywords.forEach((keyword) => {
        if (content.includes(keyword)) {
          analysis.hasRecommendation = true;
          analysis.recommendationCount++;
        }
      });
    }

    return analysis;
  }

  /**
   * 验证管道断言
   */
  validatePipelineAssertions(testCase, analysis) {
    const assertions = [];

    // 断言1: 技术映射正确性
    if (testCase.expectedMapping !== undefined) {
      assertions.push({
        description: "技术映射正确性检查",
        passed: analysis.hasTechMapping === testCase.expectedMapping,
        result: `预期=${testCase.expectedMapping}, 实际=${analysis.hasTechMapping}`,
        details:
          analysis.hasTechMapping !== testCase.expectedMapping
            ? `检测到的技术: ${analysis.detectedTech.join(", ")}`
            : null,
      });
    }

    // 断言2: 推荐生成正确性
    if (testCase.expectedRecommendation !== undefined) {
      assertions.push({
        description: "推荐生成正确性检查",
        passed: analysis.hasRecommendation === testCase.expectedRecommendation,
        result: `预期=${testCase.expectedRecommendation}, 实际=${analysis.hasRecommendation}`,
        details:
          analysis.hasRecommendation !== testCase.expectedRecommendation
            ? `推荐关键词数量: ${analysis.recommendationCount}`
            : null,
      });
    }

    return assertions;
  }

  /**
   * 记录测试结果
   */
  logTestResult(category, testCase) {
    this.testResults.push({
      category,
      testId: this.testId,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 执行测试
if (require.main === module) {
  const tester = new RealDeepIntegrationTester();
  tester.executeRealDeepTests().catch(console.error);
}

module.exports = RealDeepIntegrationTester;
