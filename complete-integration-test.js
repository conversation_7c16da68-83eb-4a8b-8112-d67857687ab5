/**
 * 完整的不可造假深度系统集成测试
 *
 * 严格按照要求执行所有测试模块：
 * - 双模型调用
 * - 信息收集触发机制（主动、关键词、上下文）
 * - 推荐系统完整链路（技术映射、业务映射、推荐矩阵）
 * - 对话终止逻辑
 * - 容错机制异常注入
 * - 并发100x冷启动测试
 */

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");

class CompleteIntegrationTester {
  constructor() {
    this.baseUrl = "http://localhost:6789";
    this.testResults = [];
    this.startTime = Date.now();
    this.testId = `complete-test-${Date.now()}`;

    // 严格测试配置
    this.config = {
      timeout: 30000,
      concurrentCount: 100,
      coldStartDelay: 100,
      retryAttempts: 0,
      cacheDisabled: true,
    };
  }

  /**
   * 执行完整的深度集成测试
   */
  async executeCompleteTests() {
    console.log("🚀 开始执行完整的不可造假深度系统集成测试");
    console.log(`测试ID: ${this.testId}`);
    console.log(`开始时间: ${new Date().toISOString()}`);
    console.log("=".repeat(80));

    try {
      // 1. 系统健康检查
      await this.testSystemHealth();

      // 2. 双模型调用测试
      await this.testDualModelCalls();

      // 3. 信息收集触发机制测试
      await this.testInfoCollectionTriggers();

      // 4. 推荐系统完整链路测试
      await this.testRecommendationPipeline();

      // 5. 对话终止逻辑测试
      await this.testConversationTermination();

      // 6. 容错机制异常注入测试
      await this.testErrorHandlingMechanisms();

      // 7. 并发100x冷启动测试
      await this.testConcurrentColdStart();

      // 8. 生成详细测试报告
      this.generateDetailedReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.logTestResult("CRITICAL_FAILURE", {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 测试1: 系统健康检查
   */
  async testSystemHealth() {
    console.log("\n📊 测试1: 系统健康检查");
    console.log("-".repeat(50));

    const healthTest = {
      name: "系统健康检查",
      startTime: Date.now(),
      testTarget: "验证系统基础运行状态",
      assertionLogic: "HTTP状态200且响应包含健康状态信息",
    };

    try {
      console.log(`🔍 测试目标: ${healthTest.testTarget}`);
      console.log(`🎯 断言逻辑: ${healthTest.assertionLogic}`);
      console.log(`🔍 请求: GET ${this.baseUrl}/health`);

      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: this.config.timeout,
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      });

      healthTest.endTime = Date.now();
      healthTest.responseTime = healthTest.endTime - healthTest.startTime;
      healthTest.httpStatus = response.status;
      healthTest.rawResponse = response.data;
      healthTest.responseHeaders = response.headers;

      // 断言验证
      const assertions = [
        {
          description: "HTTP状态码为200",
          passed: response.status === 200,
          actual: response.status,
          expected: 200,
        },
        {
          description: "响应包含status字段",
          passed: response.data && "status" in response.data,
          actual: response.data ? Object.keys(response.data) : [],
          expected: ["status"],
        },
        {
          description: "响应时间小于1000ms",
          passed: healthTest.responseTime < 1000,
          actual: healthTest.responseTime,
          expected: "<1000ms",
        },
      ];

      healthTest.assertions = assertions;
      healthTest.status = assertions.every((a) => a.passed)
        ? "SUCCESS"
        : "FAILURE";

      console.log(`📊 响应时间: ${healthTest.responseTime}ms`);
      console.log(`📋 HTTP状态: ${response.status}`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`📋 响应头:`, JSON.stringify(response.headers, null, 2));

      console.log(`🎯 断言结果:`);
      assertions.forEach((assertion) => {
        const icon = assertion.passed ? "✅" : "❌";
        console.log(
          `   ${icon} ${assertion.description}: 预期=${JSON.stringify(
            assertion.expected
          )}, 实际=${JSON.stringify(assertion.actual)}`
        );
      });
    } catch (error) {
      healthTest.endTime = Date.now();
      healthTest.responseTime = healthTest.endTime - healthTest.startTime;
      healthTest.status = "FAILURE";
      healthTest.error = error.message;
      healthTest.errorStack = error.stack;
      healthTest.httpStatus = error.response?.status || "TIMEOUT";

      console.log(`❌ 健康检查失败: ${error.message}`);
      console.log(`📊 响应时间: ${healthTest.responseTime}ms`);
      console.log(`💥 错误代码: ${error.code}`);
      if (error.response) {
        console.log(`📋 HTTP状态: ${error.response.status}`);
        console.log(
          `📄 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
      }
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("SYSTEM_HEALTH", healthTest);
  }

  /**
   * 测试2: 双模型调用
   */
  async testDualModelCalls() {
    console.log("\n🤖 测试2: 双模型调用真实性验证");
    console.log("-".repeat(50));

    const dualModelTests = [
      {
        name: "DeepSeek模型真实调用测试",
        testTarget: "验证DeepSeek模型能够正确处理意图识别",
        assertionLogic: "响应成功且包含意图分析内容",
        input: {
          message: "你好，我想找一份Java开发的工作",
          userEmail: "<EMAIL>",
        },
        expectedFeatures: ["意图识别", "工作需求"],
      },
      {
        name: "Qwen模型真实调用测试",
        testTarget: "验证Qwen模型能够正确处理技术信息提取",
        assertionLogic: "响应成功且包含技术栈分析",
        input: {
          message: "我是Python算法工程师，有5年经验，期望薪资40k",
          userEmail: "<EMAIL>",
        },
        expectedFeatures: ["技术栈", "经验分析", "薪资分析"],
      },
      {
        name: "模型切换逻辑测试",
        testTarget: "验证系统能够根据内容选择合适的模型",
        assertionLogic: "响应成功且体现模型协作",
        input: {
          message: "推荐一些适合我的职位，我是前端开发",
          userEmail: "<EMAIL>",
        },
        expectedFeatures: ["推荐生成", "职位匹配"],
      },
    ];

    for (const testCase of dualModelTests) {
      await this.executeDualModelTest(testCase);
      console.log(`⏰ 冷启动间隔: ${this.config.coldStartDelay}ms`);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行双模型测试
   */
  async executeDualModelTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`🎯 测试目标: ${testCase.testTarget}`);
      console.log(`🎯 断言逻辑: ${testCase.assertionLogic}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
      console.log(`📝 会话ID: ${sessionId}`);
      console.log(`🎯 预期功能: ${testCase.expectedFeatures.join(", ")}`);

      const requestPayload = {
        ...testCase.input,
        sessionId: sessionId,
        timestamp: Date.now(),
      };

      console.log(`🔍 请求: POST ${this.baseUrl}/api/chat/message`);
      console.log(`📄 请求体:`, JSON.stringify(requestPayload, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat/message`,
        requestPayload,
        {
          timeout: this.config.timeout,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
            "X-Test-ID": this.testId,
            "X-Test-Case": Buffer.from(testCase.name).toString("base64"),
          },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;
      testCase.responseHeaders = response.headers;

      // 详细分析响应
      const analysis = this.analyzeModelResponse(
        response.data,
        testCase.expectedFeatures
      );
      testCase.responseAnalysis = analysis;

      // 验证断言
      const assertions = this.validateDualModelAssertions(
        testCase,
        response.data,
        analysis
      );
      testCase.assertions = assertions;
      testCase.status = assertions.every((a) => a.passed)
        ? "SUCCESS"
        : "FAILURE";

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 HTTP状态: ${response.status}`);
      console.log(`📄 原始响应:`, JSON.stringify(response.data, null, 2));
      console.log(`📋 响应头:`, JSON.stringify(response.headers, null, 2));
      console.log(`🔍 响应分析:`, JSON.stringify(analysis, null, 2));

      console.log(`🎯 断言结果:`);
      assertions.forEach((assertion) => {
        const icon = assertion.passed ? "✅" : "❌";
        console.log(`   ${icon} ${assertion.description}: ${assertion.result}`);
        if (!assertion.passed && assertion.details) {
          console.log(`      详情: ${assertion.details}`);
        }
      });
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorCode = error.code;
      testCase.errorStack = error.stack;

      if (error.response) {
        testCase.httpStatus = error.response.status;
        testCase.errorResponse = error.response.data;
        testCase.errorHeaders = error.response.headers;
      }

      console.log(`❌ 测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`💥 错误代码: ${error.code}`);
      if (error.response) {
        console.log(`📋 HTTP状态: ${error.response.status}`);
        console.log(
          `📄 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
        console.log(
          `📋 错误响应头:`,
          JSON.stringify(error.response.headers, null, 2)
        );
      }
      console.log(`📋 完整错误栈:`, error.stack);
    }

    this.logTestResult("DUAL_MODEL_CALL", testCase);
  }

  /**
   * 分析模型响应
   */
  analyzeModelResponse(responseData, expectedFeatures) {
    const analysis = {
      hasResponse: !!responseData.response,
      responseType: responseData.response?.type || "unknown",
      contentLength: responseData.response?.content?.length || 0,
      hasMetadata: !!responseData.metadata,
      processingTime: responseData.processingTime || 0,
      modelUsed: responseData.modelUsed || "unknown",
      featuresDetected: [],
    };

    if (responseData.response?.content) {
      const content = responseData.response.content.toLowerCase();

      // 检测功能特征
      expectedFeatures.forEach((feature) => {
        let detected = false;
        switch (feature) {
          case "意图识别":
            detected =
              content.includes("意图") ||
              content.includes("想要") ||
              content.includes("需要") ||
              content.includes("找工作");
            break;
          case "工作需求":
            detected =
              content.includes("工作") ||
              content.includes("职位") ||
              content.includes("岗位");
            break;
          case "技术栈":
            detected =
              content.includes("技术") ||
              content.includes("python") ||
              content.includes("java") ||
              content.includes("算法");
            break;
          case "经验分析":
            detected =
              content.includes("经验") ||
              content.includes("年") ||
              content.includes("工作经历");
            break;
          case "薪资分析":
            detected =
              content.includes("薪资") ||
              content.includes("薪水") ||
              content.includes("工资") ||
              content.includes("40k");
            break;
          case "推荐生成":
            detected =
              content.includes("推荐") ||
              content.includes("建议") ||
              content.includes("适合");
            break;
          case "职位匹配":
            detected =
              content.includes("匹配") ||
              content.includes("符合") ||
              content.includes("前端");
            break;
        }

        if (detected) {
          analysis.featuresDetected.push(feature);
        }
      });
    }

    return analysis;
  }

  /**
   * 验证双模型断言
   */
  validateDualModelAssertions(testCase, responseData, analysis) {
    const assertions = [];

    // 断言1: 响应成功
    assertions.push({
      description: "响应成功性检查",
      passed: responseData.success === true,
      result: responseData.success ? "响应成功" : "响应失败",
      details: responseData.success ? null : responseData.error,
    });

    // 断言2: 响应时间合理
    assertions.push({
      description: "响应时间检查",
      passed: testCase.responseTime < 30000,
      result: `${testCase.responseTime}ms`,
      details: testCase.responseTime >= 30000 ? "响应时间超过30秒" : null,
    });

    // 断言3: 响应内容存在
    assertions.push({
      description: "响应内容存在性检查",
      passed: !!(responseData.response && responseData.response.content),
      result: responseData.response?.content ? "内容存在" : "内容缺失",
      details: !responseData.response?.content ? "响应中缺少content字段" : null,
    });

    // 断言4: 预期功能检测
    testCase.expectedFeatures.forEach((feature) => {
      assertions.push({
        description: `功能检测: ${feature}`,
        passed: analysis.featuresDetected.includes(feature),
        result: analysis.featuresDetected.includes(feature)
          ? "检测到"
          : "未检测到",
        details: analysis.featuresDetected.includes(feature)
          ? null
          : `响应中未发现${feature}相关内容`,
      });
    });

    return assertions;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 记录测试结果
   */
  logTestResult(category, testCase) {
    this.testResults.push({
      category,
      testId: this.testId,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 执行测试
if (require.main === module) {
  const tester = new CompleteIntegrationTester();
  tester.executeCompleteTests().catch(console.error);
}

module.exports = CompleteIntegrationTester;
