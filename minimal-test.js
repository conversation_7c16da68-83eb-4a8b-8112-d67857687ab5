/**
 * 最小化测试 - 验证系统基本启动能力
 */

console.log('🚀 开始最小化系统测试');

// 测试1: 检查Node.js环境
console.log('\n📊 测试1: Node.js环境检查');
console.log(`Node.js版本: ${process.version}`);
console.log(`当前目录: ${process.cwd()}`);
console.log(`平台: ${process.platform}`);

// 测试2: 检查依赖模块
console.log('\n📦 测试2: 依赖模块检查');
try {
  const axios = require('axios');
  console.log('✅ axios模块加载成功');
} catch (error) {
  console.log('❌ axios模块加载失败:', error.message);
}

try {
  const { v4: uuidv4 } = require('uuid');
  console.log('✅ uuid模块加载成功');
} catch (error) {
  console.log('❌ uuid模块加载失败:', error.message);
}

// 测试3: 检查环境变量
console.log('\n🔧 测试3: 环境变量检查');
require('dotenv').config({ path: './ai-recruitment-assistant/.env.local' });

const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY', 
  'DEEPSEEK_API_KEY',
  'PORT'
];

requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar}: 已设置`);
  } else {
    console.log(`❌ ${envVar}: 未设置`);
  }
});

// 测试4: 尝试加载核心模块
console.log('\n🧩 测试4: 核心模块加载检查');
try {
  const AppConfig = require('./ai-recruitment-assistant/core/系统核心/app-config');
  console.log('✅ AppConfig模块加载成功');
  
  // 尝试创建配置实例
  const config = new AppConfig();
  console.log('✅ AppConfig实例创建成功');
} catch (error) {
  console.log('❌ AppConfig模块加载失败:', error.message);
  console.log('错误堆栈:', error.stack);
}

// 测试5: 网络连接测试
console.log('\n🌐 测试5: 网络连接测试');
async function testNetwork() {
  try {
    const axios = require('axios');
    
    // 测试外部网络
    const response = await axios.get('https://httpbin.org/get', { timeout: 5000 });
    console.log('✅ 外部网络连接正常');
    
    // 测试本地端口
    try {
      await axios.get('http://localhost:6789/health', { timeout: 2000 });
      console.log('✅ 本地6789端口有响应');
    } catch (error) {
      console.log('❌ 本地6789端口无响应:', error.code);
    }
    
  } catch (error) {
    console.log('❌ 网络连接测试失败:', error.message);
  }
}

// 测试6: 尝试启动系统
console.log('\n🚀 测试6: 系统启动测试');
async function testSystemStart() {
  try {
    console.log('正在尝试启动系统...');
    
    // 导入系统主类
    const AIRecruitmentSystem = require('./ai-recruitment-assistant/core/系统核心/index');
    console.log('✅ 系统主类导入成功');
    
    // 创建系统实例
    const system = new AIRecruitmentSystem();
    console.log('✅ 系统实例创建成功');
    
    // 尝试初始化（设置超时）
    const initPromise = system.initialize();
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('初始化超时')), 10000)
    );
    
    await Promise.race([initPromise, timeoutPromise]);
    console.log('✅ 系统初始化成功');
    
    // 尝试启动服务器
    await system.start();
    console.log('✅ 系统启动成功');
    
    // 等待一段时间后测试
    setTimeout(async () => {
      try {
        const axios = require('axios');
        const response = await axios.get('http://localhost:6789/health', { timeout: 3000 });
        console.log('✅ 系统健康检查通过:', response.data);
        
        // 进行真实的API测试
        await performRealAPITests();
        
      } catch (error) {
        console.log('❌ 系统健康检查失败:', error.message);
      }
    }, 2000);
    
  } catch (error) {
    console.log('❌ 系统启动失败:', error.message);
    console.log('错误堆栈:', error.stack);
  }
}

// 真实API测试
async function performRealAPITests() {
  console.log('\n🎯 开始真实API测试');
  const axios = require('axios');
  const { v4: uuidv4 } = require('uuid');
  
  const testCases = [
    {
      name: '初始化对话测试',
      data: { message: '__INIT__', userEmail: '<EMAIL>' }
    },
    {
      name: '问候语测试', 
      data: { message: '你好', userEmail: '<EMAIL>' }
    },
    {
      name: '职位询问测试',
      data: { message: '有什么职位推荐吗', userEmail: '<EMAIL>' }
    },
    {
      name: '信息提供测试',
      data: { message: '我是Java开发工程师，有3年经验', userEmail: '<EMAIL>' }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🔍 执行: ${testCase.name}`);
      const startTime = Date.now();
      
      const response = await axios.post('http://localhost:6789/api/chat', {
        ...testCase.data,
        sessionId: uuidv4()
      }, {
        timeout: 15000,
        headers: { 'Content-Type': 'application/json' }
      });
      
      const responseTime = Date.now() - startTime;
      
      console.log(`✅ ${testCase.name} 成功`);
      console.log(`📊 响应时间: ${responseTime}ms`);
      console.log(`📋 响应状态: ${response.data.success}`);
      console.log(`📝 响应类型: ${response.data.response?.type || 'unknown'}`);
      console.log(`📄 响应内容长度: ${response.data.response?.content?.length || 0}字符`);
      
      // 显示实际响应内容的前100个字符
      if (response.data.response?.content) {
        const preview = response.data.response.content.substring(0, 100);
        console.log(`📖 响应预览: "${preview}${response.data.response.content.length > 100 ? '...' : ''}"`);
      }
      
    } catch (error) {
      console.log(`❌ ${testCase.name} 失败: ${error.message}`);
      if (error.response) {
        console.log(`📋 HTTP状态: ${error.response.status}`);
        console.log(`📄 错误响应:`, JSON.stringify(error.response.data, null, 2));
      }
    }
  }
  
  console.log('\n🎉 真实API测试完成');
}

// 执行所有测试
async function runAllTests() {
  await testNetwork();
  await testSystemStart();
}

runAllTests().catch(error => {
  console.error('❌ 测试执行失败:', error);
});
