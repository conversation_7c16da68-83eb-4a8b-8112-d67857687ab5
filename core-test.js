#!/usr/bin/env node

/**
 * 核心功能测试 - 最基础的系统验证
 */

console.log('🚀 开始核心功能测试');
console.log('时间:', new Date().toISOString());

// 测试结果收集
const results = [];

function logResult(test, status, details = {}) {
  const result = {
    test,
    status,
    timestamp: new Date().toISOString(),
    ...details
  };
  results.push(result);
  
  const icon = status === 'SUCCESS' ? '✅' : status === 'FAILURE' ? '❌' : '⚠️';
  console.log(`${icon} ${test}: ${status}`);
  
  if (details.error) {
    console.log(`   错误: ${details.error}`);
  }
  if (details.time) {
    console.log(`   耗时: ${details.time}ms`);
  }
}

// 测试1: Node.js环境
console.log('\n📊 测试1: Node.js环境');
try {
  const nodeVersion = process.version;
  const platform = process.platform;
  logResult('Node.js环境检查', 'SUCCESS', {
    nodeVersion,
    platform,
    cwd: process.cwd()
  });
} catch (error) {
  logResult('Node.js环境检查', 'FAILURE', { error: error.message });
}

// 测试2: 依赖模块
console.log('\n📦 测试2: 依赖模块');
const dependencies = ['axios', 'uuid', 'dotenv'];

dependencies.forEach(dep => {
  try {
    require(dep);
    logResult(`依赖模块: ${dep}`, 'SUCCESS');
  } catch (error) {
    logResult(`依赖模块: ${dep}`, 'FAILURE', { error: error.message });
  }
});

// 测试3: 环境变量
console.log('\n🔧 测试3: 环境变量');
try {
  require('dotenv').config({ path: './ai-recruitment-assistant/.env.local' });
  
  const envVars = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'DEEPSEEK_API_KEY',
    'PORT'
  ];
  
  let envCount = 0;
  envVars.forEach(envVar => {
    if (process.env[envVar]) {
      envCount++;
      logResult(`环境变量: ${envVar}`, 'SUCCESS');
    } else {
      logResult(`环境变量: ${envVar}`, 'FAILURE', { error: '未设置' });
    }
  });
  
  logResult('环境变量总体检查', envCount === envVars.length ? 'SUCCESS' : 'PARTIAL', {
    total: envVars.length,
    present: envCount
  });
  
} catch (error) {
  logResult('环境变量加载', 'FAILURE', { error: error.message });
}

// 测试4: 核心文件存在性
console.log('\n📁 测试4: 核心文件存在性');
const fs = require('fs');
const coreFiles = [
  './ai-recruitment-assistant/core/系统核心/app-config.js',
  './ai-recruitment-assistant/core/系统核心/message-processor.js',
  './ai-recruitment-assistant/core/数据管理/database-manager.js',
  './ai-recruitment-assistant/core/数据管理/ai-services.js',
  './ai-recruitment-assistant/core/业务服务/user-manager.js'
];

coreFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      logResult(`文件存在: ${file.split('/').pop()}`, 'SUCCESS');
    } else {
      logResult(`文件存在: ${file.split('/').pop()}`, 'FAILURE', { error: '文件不存在' });
    }
  } catch (error) {
    logResult(`文件检查: ${file.split('/').pop()}`, 'FAILURE', { error: error.message });
  }
});

// 测试5: 模块语法检查
console.log('\n🧩 测试5: 模块语法检查');
coreFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      require(file);
      logResult(`语法检查: ${file.split('/').pop()}`, 'SUCCESS');
    }
  } catch (error) {
    logResult(`语法检查: ${file.split('/').pop()}`, 'FAILURE', { 
      error: error.message.substring(0, 100) + '...' 
    });
  }
});

// 测试6: 基础功能测试
console.log('\n⚙️ 测试6: 基础功能测试');

async function testBasicFunctionality() {
  try {
    // 测试AppConfig
    const AppConfig = require('./ai-recruitment-assistant/core/系统核心/app-config');
    const config = new AppConfig();
    logResult('AppConfig实例化', 'SUCCESS');
    
    // 测试配置初始化
    await config.initialize();
    logResult('AppConfig初始化', 'SUCCESS');
    
  } catch (error) {
    logResult('基础功能测试', 'FAILURE', { error: error.message });
  }
}

// 测试7: 网络连接测试
console.log('\n🌐 测试7: 网络连接测试');

async function testNetworkConnectivity() {
  try {
    const axios = require('axios');
    
    // 测试外部网络
    const response = await axios.get('https://httpbin.org/get', { timeout: 5000 });
    logResult('外部网络连接', 'SUCCESS', { 
      status: response.status,
      time: response.headers['x-response-time'] || 'N/A'
    });
    
    // 测试本地端口
    try {
      await axios.get('http://localhost:6789/health', { timeout: 2000 });
      logResult('本地服务连接', 'SUCCESS');
    } catch (error) {
      logResult('本地服务连接', 'FAILURE', { 
        error: error.code || error.message 
      });
    }
    
  } catch (error) {
    logResult('网络连接测试', 'FAILURE', { error: error.message });
  }
}

// 执行异步测试
async function runAsyncTests() {
  await testBasicFunctionality();
  await testNetworkConnectivity();
  
  // 生成最终报告
  generateFinalReport();
}

function generateFinalReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 核心功能测试报告');
  console.log('='.repeat(60));
  
  const totalTests = results.length;
  const successTests = results.filter(r => r.status === 'SUCCESS').length;
  const failureTests = results.filter(r => r.status === 'FAILURE').length;
  const partialTests = results.filter(r => r.status === 'PARTIAL').length;
  
  console.log(`\n📈 测试统计:`);
  console.log(`总测试数: ${totalTests}`);
  console.log(`成功: ${successTests}`);
  console.log(`失败: ${failureTests}`);
  console.log(`部分成功: ${partialTests}`);
  console.log(`成功率: ${((successTests / totalTests) * 100).toFixed(1)}%`);
  
  console.log(`\n📝 失败测试详情:`);
  results.filter(r => r.status === 'FAILURE').forEach(result => {
    console.log(`❌ ${result.test}: ${result.error || '未知错误'}`);
  });
  
  console.log(`\n🎯 测试结论:`);
  if (successTests / totalTests >= 0.8) {
    console.log('✅ 系统核心功能基本正常');
  } else if (successTests / totalTests >= 0.6) {
    console.log('⚠️ 系统核心功能部分正常，需要修复');
  } else {
    console.log('❌ 系统核心功能存在严重问题');
  }
  
  // 保存报告
  try {
    const reportFile = `core-test-report-${Date.now()}.json`;
    require('fs').writeFileSync(reportFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: { totalTests, successTests, failureTests, partialTests },
      results: results
    }, null, 2));
    console.log(`📄 详细报告已保存: ${reportFile}`);
  } catch (error) {
    console.log(`❌ 报告保存失败: ${error.message}`);
  }
  
  console.log('\n' + '='.repeat(60));
}

// 运行测试
runAsyncTests().catch(error => {
  console.error('❌ 测试执行失败:', error);
  logResult('测试执行', 'FAILURE', { error: error.message });
  generateFinalReport();
});
