// 快速测试脚本
console.log('开始测试');

const axios = require('axios');

async function test() {
  try {
    console.log('测试健康检查...');
    const response = await axios.get('http://localhost:6789/health', { timeout: 5000 });
    console.log('健康检查成功:', response.data);
    
    console.log('测试聊天API...');
    const chatResponse = await axios.post('http://localhost:6789/api/chat', {
      message: '你好',
      userEmail: '<EMAIL>'
    }, { timeout: 10000 });
    
    console.log('聊天测试成功:', {
      success: chatResponse.data.success,
      responseType: chatResponse.data.response?.type,
      contentLength: chatResponse.data.response?.content?.length
    });
    
  } catch (error) {
    console.log('测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('系统未启动，请先启动系统');
    }
  }
}

test();
