{"timestamp": "2025-07-31T08:26:25.223Z", "summary": {"totalTests": 23, "successTests": 21, "failureTests": 1, "partialTests": 0}, "results": [{"test": "Node.js环境检查", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.421Z", "nodeVersion": "v20.19.2", "platform": "darwin", "cwd": "/Users/<USER>/Desktop/ai-recruitment-assistant-fixed"}, {"test": "依赖模块: axios", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.458Z"}, {"test": "依赖模块: uuid", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.462Z"}, {"test": "依赖模块: dotenv", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.463Z"}, {"test": "环境变量: SUPABASE_URL", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "环境变量: SUPABASE_SERVICE_ROLE_KEY", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "环境变量: DEEPSEEK_API_KEY", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "环境变量: PORT", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "环境变量总体检查", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z", "total": 4, "present": 4}, {"test": "文件存在: app-config.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "文件存在: message-processor.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "文件存在: database-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "文件存在: ai-services.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "文件存在: user-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.464Z"}, {"test": "语法检查: app-config.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.499Z"}, {"test": "语法检查: message-processor.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.543Z"}, {"test": "语法检查: database-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.580Z"}, {"test": "语法检查: ai-services.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.581Z"}, {"test": "语法检查: user-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.581Z"}, {"test": "AppConfig实例化", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.581Z"}, {"test": "AppConfig初始化", "status": "SUCCESS", "timestamp": "2025-07-31T08:26:23.594Z"}, {"test": "外部网络连接", "status": 200, "timestamp": "2025-07-31T08:26:25.214Z", "time": "N/A"}, {"test": "本地服务连接", "status": "FAILURE", "timestamp": "2025-07-31T08:26:25.222Z", "error": "ECONNREFUSED"}]}