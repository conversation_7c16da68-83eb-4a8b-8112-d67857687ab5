{"testId": "integration-test-1753958942959", "startTime": "2025-07-31T10:49:02.959Z", "endTime": "2025-07-31T10:49:07.383Z", "totalTestTime": 4424, "statistics": {"totalTests": 25, "passedTests": 4, "failedTests": 21, "testSuccessRate": 16, "totalRequests": 124, "successfulRequests": 0, "apiSuccessRate": 0, "avgResponseTime": 26.975806451612904, "errors": 20}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 0, "apiSuccessRatePassed": false, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 26.975806451612904, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:02.988Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753958942968, "endTime": 1753958942988, "responseTime": 20, "status": "FAILED", "error": "", "errorStack": "AggregateError\n    at AxiosError.from (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:899:14)\n    at RedirectableRequest.handleRequestError (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3228:25)\n    at RedirectableRequest.emit (node:events:536:35)\n    at eventHandlers.<computed> (/Users/<USER>/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:101:11)\n    at Socket.socketErrorListener (node:_http_client:504:5)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:450:20)\n    at async ComprehensiveSystemIntegrationTest.testSystemHealth (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:115:24)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:62:7)"}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:02.991Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "e7a30ec3-a7b7-4f97-b2fa-81be02c2e8cf"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753958942989, "category": "DUAL_MODEL_AI", "endTime": 1753958942991, "responseTime": 2, "status": "FAILED", "error": "", "errorStack": "AggregateError\n    at AxiosError.from (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:899:14)\n    at RedirectableRequest.handleRequestError (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3228:25)\n    at RedirectableRequest.emit (node:events:536:35)\n    at eventHandlers.<computed> (/Users/<USER>/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:101:11)\n    at Socket.socketErrorListener (node:_http_client:504:5)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:452:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:03.196Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "84a91d06-072c-464f-b8f0-3826050b5368"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753958943193, "category": "DUAL_MODEL_AI", "endTime": 1753958943196, "responseTime": 3, "status": "FAILED", "error": "", "errorStack": "AggregateError\n    at AxiosError.from (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:899:14)\n    at RedirectableRequest.handleRequestError (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3228:25)\n    at RedirectableRequest.emit (node:events:536:35)\n    at eventHandlers.<computed> (/Users/<USER>/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:101:11)\n    at Socket.socketErrorListener (node:_http_client:504:5)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:452:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:03.403Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "53040bbb-c730-4ac9-bc54-10926eee523b"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753958943398, "category": "DUAL_MODEL_AI", "endTime": 1753958943402, "responseTime": 4, "status": "FAILED", "error": "", "errorStack": "AggregateError\n    at AxiosError.from (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:899:14)\n    at RedirectableRequest.handleRequestError (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3228:25)\n    at RedirectableRequest.emit (node:events:536:35)\n    at eventHandlers.<computed> (/Users/<USER>/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:101:11)\n    at Socket.socketErrorListener (node:_http_client:504:5)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:452:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:03.607Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "11fb2d9b-a50a-46d5-a5a2-489976500f0d"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753958943604, "category": "DUAL_MODEL_AI", "endTime": 1753958943607, "responseTime": 3, "status": "FAILED", "error": "", "errorStack": "AggregateError\n    at AxiosError.from (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:899:14)\n    at RedirectableRequest.handleRequestError (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:3228:25)\n    at RedirectableRequest.emit (node:events:536:35)\n    at eventHandlers.<computed> (/Users/<USER>/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:524:28)\n    at emitErrorEvent (node:_http_client:101:11)\n    at Socket.socketErrorListener (node:_http_client:504:5)\n    at Socket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at Axios.request (/Users/<USER>/node_modules/axios/dist/node/axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ComprehensiveSystemIntegrationTest.makeRequest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:452:20)\n    at async ComprehensiveSystemIntegrationTest.executeDualModelTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:235:24)\n    at async ComprehensiveSystemIntegrationTest.testDualModelAICalls (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:219:7)\n    at async ComprehensiveSystemIntegrationTest.executeCompleteIntegrationTest (/Users/<USER>/Desktop/ai-recruitment-assistant-fixed/comprehensive-system-integration-test.js:65:7)"}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:03.811Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "cd4b5f7f-b50a-403e-9a33-7a50d9bbb4e3"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753958943809, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958943811, "responseTime": 2, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:04.017Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "347a4b75-94c7-4f26-801a-53180d97e210"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753958944012, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958944017, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:04.223Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "f2f6b8c9-ab1b-4c04-b95a-4b95e4f6f952"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753958944219, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958944222, "responseTime": 3, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:04.431Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "de945385-14f8-4915-88d8-11bb02095e07"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753958944424, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958944431, "responseTime": 7, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:04.638Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "cc7d9e80-d16e-4b5b-b501-786ed460f0f2"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753958944633, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958944638, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:04.846Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "64fa770e-4969-4a3a-a3f6-efb9f21fcd1a"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753958944840, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753958944846, "responseTime": 6, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.053Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "dc8546e5-93ab-43a8-a6ef-cf231fc76c27"}, "startTime": 1753958945047, "endTime": 1753958945053, "responseTime": 6, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.059Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "3507232c-eaf8-4d6a-ae0f-936f248fc71b"}, "dimension": "technology", "startTime": 1753958945055, "category": "RECOMMENDATION_MATRIX", "endTime": 1753958945059, "responseTime": 4, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.266Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "d206619a-db90-489f-a855-c0c1d4da5607"}, "dimension": "experience", "startTime": 1753958945261, "category": "RECOMMENDATION_MATRIX", "endTime": 1753958945266, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.472Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "2d492eae-7a10-41d0-b6bc-96298d1ea386"}, "dimension": "industry", "startTime": 1753958945468, "category": "RECOMMENDATION_MATRIX", "endTime": 1753958945472, "responseTime": 4, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.679Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "6440326a-e746-46d3-8c9c-72f7c194c738"}, "dimension": "location", "startTime": 1753958945673, "category": "RECOMMENDATION_MATRIX", "endTime": 1753958945678, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.886Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "7bdfa227-28d1-4bad-af5e-60908d4a40a7"}, "startTime": 1753958945880, "endTime": 1753958945885, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:05.891Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "c9ae6f31-9a21-4110-9f9d-ed62525d2b0b"}, "startTime": 1753958945886, "category": "CONVERSATION_TERMINATION", "endTime": 1753958945891, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:06.097Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "d929481a-92fb-4dfe-936d-bae56ed7834f"}, "startTime": 1753958946093, "category": "CONVERSATION_TERMINATION", "endTime": 1753958946097, "responseTime": 4, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:06.303Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "8bc80b83-42b3-4c50-9378-ef611280aa1b"}, "startTime": 1753958946298, "category": "CONVERSATION_TERMINATION", "endTime": 1753958946303, "responseTime": 5, "status": "FAILED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:06.507Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "2911c9b6-2c47-43a6-8d7b-8a2270a2de83"}, "startTime": 1753958946505, "category": "ERROR_HANDLING", "endTime": 1753958946507, "responseTime": 2, "status": "PASSED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:06.711Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "1c3818f0-8ffc-4640-a23a-0c1a063575ee"}, "startTime": 1753958946708, "category": "ERROR_HANDLING", "endTime": 1753958946711, "responseTime": 3, "status": "PASSED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:06.916Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "a78685f9-525e-49ef-9574-a9c58b7f4331"}, "startTime": 1753958946912, "category": "ERROR_HANDLING", "endTime": 1753958946916, "responseTime": 4, "status": "PASSED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:07.121Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "207acee3-25c4-4a18-874d-ad609083bda1"}, "startTime": 1753958947117, "category": "ERROR_HANDLING", "endTime": 1753958947121, "responseTime": 4, "status": "PASSED", "error": ""}, {"testId": "integration-test-1753958942959", "timestamp": "2025-07-31T10:49:07.383Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753958947324, "endTime": 1753958947383, "responseTime": 59, "totalRequests": 100, "successfulRequests": 0, "failedRequests": 100, "successRate": 0, "status": "FAILED"}]}