{"testId": "problem-discovery-1753965814591", "startTime": "2025-07-31T12:43:34.591Z", "endTime": "2025-07-31T12:45:45.836Z", "totalTime": 131245, "problemsSummary": {"total": 5, "critical": 0, "high": 5, "medium": 0, "low": 0}, "problems": [{"category": "信息提取缺陷", "testCase": "复杂信息提取", "input": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "problems": ["非AI算法技术未能触发专业化拒绝", "专业化拒绝中未体现对用户技术方向(Python)的理解"], "systemResponse": "抱歉，系统遇到了一些技术问题，请稍后再试。", "severity": "HIGH"}, {"category": "意图识别错误", "testCase": "明确求职意图", "input": "我想找一份工作", "expected": "JOB_SEARCH", "actual": "job_search", "severity": "HIGH"}, {"category": "意图识别错误", "testCase": "职位推荐请求", "input": "有什么职位推荐吗", "expected": "JOB_SEARCH", "actual": "job_search", "severity": "HIGH"}, {"category": "意图识别错误", "testCase": "技术咨询", "input": "我是Python开发，想了解市场行情", "expected": "JOB_SEARCH", "actual": "profile_update", "severity": "HIGH"}, {"category": "意图识别错误", "testCase": "无关内容", "input": "今天天气真好", "expected": "GREETING", "actual": "greeting", "severity": "HIGH"}], "testResults": [{"category": "INFORMATION_EXTRACTION", "testCase": "基础信息提取", "input": {"message": "我是Java开发工程师，有3年经验", "userEmail": "<EMAIL>", "sessionId": "fd91d2e5-708c-4209-8327-85ebb4fab895"}, "response": {"success": true, "sessionId": "fd91d2e5-708c-4209-8327-85ebb4fab895", "response": {"type": "professional_decline", "content": "我了解您在Java开发方面有3年的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "Java开发"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:43:43.309Z"}, "responseTime": 8724, "extractionResults": {"extractedInfo": {"技术栈": "Java", "工作经验": "3年", "职位": "开发工程师"}, "problems": [], "responseType": "professional_decline", "intent": "profile_update", "isAIRelated": false, "responseContent": "我了解您在Java开发方面有3年的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！"}, "status": "PASS"}, {"category": "INFORMATION_EXTRACTION", "testCase": "复杂信息提取", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "7a53defa-391d-4cfb-ae95-a372ac06acf5"}, "response": {"success": true, "sessionId": "7a53defa-391d-4cfb-ae95-a372ac06acf5", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T12:43:50.827Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:43:51.650Z"}, "responseTime": 7335, "extractionResults": {"extractedInfo": {"技术栈": "Python", "职位": "算法工程师", "工作经验": "5年", "期望薪资": "40k", "工作地点": "北京"}, "problems": ["非AI算法技术未能触发专业化拒绝", "专业化拒绝中未体现对用户技术方向(Python)的理解"], "responseType": "error", "intent": "profile_update", "isAIRelated": false, "responseContent": "抱歉，系统遇到了一些技术问题，请稍后再试。"}, "status": "FAIL"}, {"category": "INFORMATION_EXTRACTION", "testCase": "公司背景信息", "input": {"message": "我在阿里巴巴工作过2年，现在想转到字节跳动", "userEmail": "<EMAIL>", "sessionId": "e7150034-3650-42f4-9cf8-36e032ce40db"}, "response": {"success": true, "sessionId": "e7150034-3650-42f4-9cf8-36e032ce40db", "response": {"type": "professional_decline", "content": "我了解您在阿里巴巴方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "阿里巴巴"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:44:00.902Z"}, "responseTime": 8249, "extractionResults": {"extractedInfo": {"当前公司": "阿里巴巴", "工作经验": "2年", "目标公司": "字节跳动"}, "problems": [], "responseType": "professional_decline", "intent": "profile_update", "isAIRelated": false, "responseContent": "我了解您在阿里巴巴方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！"}, "status": "PASS"}, {"category": "INFORMATION_EXTRACTION", "testCase": "技术栈组合", "input": {"message": "我熟悉React、Node.js、MySQL，做过微服务架构", "userEmail": "<EMAIL>", "sessionId": "f8ee3b3f-21df-4aa9-badb-9edc5d90f8ca"}, "response": {"success": true, "sessionId": "f8ee3b3f-21df-4aa9-badb-9edc5d90f8ca", "response": {"type": "professional_decline", "content": "我了解您在React、Node.js、MySQL方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！", "suggestions": ["我对机器学习感兴趣", "我有AI项目经验", "了解AI算法职位要求", "谢谢，我再考虑一下"], "metadata": {"responseSource": "professional_decline", "userTechDirection": "React、Node.js、MySQL"}}, "intent": "profile_update", "timestamp": "2025-07-31T12:44:06.633Z"}, "responseTime": 4730, "extractionResults": {"extractedInfo": {"前端技术": "React", "后端技术": "Node.js", "数据库": "MySQL", "架构经验": "微服务"}, "problems": [], "responseType": "professional_decline", "intent": "profile_update", "isAIRelated": false, "responseContent": "我了解您在React、Node.js、MySQL方面有相关经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。\n\n如果您对AI算法方向感兴趣，或者有相关项目经验，我很乐意为您提供帮助！"}, "status": "PASS"}]}