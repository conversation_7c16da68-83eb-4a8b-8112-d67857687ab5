/**
 * 阶段6验证：推荐资格检查逻辑测试
 */

const UserManager = require('./core/业务服务/user-manager.js');

// 模拟依赖
const mockDatabase = {};
const mockConfig = {};
const userManager = new UserManager(mockDatabase, mockConfig);

console.log('🧪 阶段6验证：推荐资格检查逻辑测试\n');

// 测试场景1：条件1 - 公司+技术+有效职级
console.log('📋 测试场景1：条件1 - 公司+技术+有效职级');
const scenario1 = {
  所在公司: '阿里巴巴',
  技术方向: 'Java后端',
  当前职级: 'P6',
  期望薪资: null
};

const result1 = userManager.checkRecommendationEligibility(scenario1);
console.log('输入:', scenario1);
console.log('结果:', result1);
console.log('预期: isEligible=true, triggerType=company_tech_level');
console.log('✅ 测试通过:', result1.isEligible === true && result1.triggerType === 'company_tech_level');
console.log('');

// 测试场景2：条件2 - 公司+技术+期望薪酬
console.log('📋 测试场景2：条件2 - 公司+技术+期望薪酬');
const scenario2 = {
  所在公司: '腾讯',
  技术方向: 'Python后端',
  当前职级: null,
  期望薪资: '30-40万'
};

const result2 = userManager.checkRecommendationEligibility(scenario2);
console.log('输入:', scenario2);
console.log('结果:', result2);
console.log('预期: isEligible=true, triggerType=company_tech_salary');
console.log('✅ 测试通过:', result2.isEligible === true && result2.triggerType === 'company_tech_salary');
console.log('');

// 测试场景3：条件3 - 完整信息（公司+技术+有效职级+期望薪酬）
console.log('📋 测试场景3：条件3 - 完整信息');
const scenario3 = {
  所在公司: '字节跳动',
  技术方向: 'Go后端',
  当前职级: '2-1',
  期望薪资: '35-50万'
};

const result3 = userManager.checkRecommendationEligibility(scenario3);
console.log('输入:', scenario3);
console.log('结果:', result3);
console.log('预期: isEligible=true, triggerType=complete_info');
console.log('✅ 测试通过:', result3.isEligible === true && result3.triggerType === 'complete_info');
console.log('');

// 额外测试：职级无效时的兜底逻辑
console.log('📋 额外测试：职级无效时的兜底逻辑');
const scenario4 = {
  所在公司: '美团',
  技术方向: 'Node.js后端',
  当前职级: '无效职级XYZ',
  期望薪资: '25-35万'
};

const result4 = userManager.checkRecommendationEligibility(scenario4);
console.log('输入:', scenario4);
console.log('结果:', result4);
console.log('预期: isEligible=true, triggerType=fallback_with_salary');
console.log('✅ 测试通过:', result4.isEligible === true && result4.triggerType === 'fallback_with_salary');
console.log('');

console.log('🎯 阶段6验证完成！');
