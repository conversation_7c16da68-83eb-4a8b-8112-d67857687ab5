/**
 * 直接系统测试 - 绕过HTTP服务器直接测试核心模块
 *
 * 这个测试直接调用系统核心模块，避免HTTP服务器启动问题
 */

const path = require("path");
require("dotenv").config({
  path: path.join(__dirname, "ai-recruitment-assistant", ".env.local"),
});

// 导入核心模块
const AppConfig = require("./ai-recruitment-assistant/core/系统核心/app-config");
const MessageProcessor = require("./ai-recruitment-assistant/core/系统核心/message-processor");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");
const UserManager = require("./ai-recruitment-assistant/core/业务服务/user-manager");

class DirectSystemTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();

    // 核心组件
    this.config = null;
    this.database = null;
    this.userManager = null;
    this.messageProcessor = null;
  }

  /**
   * 执行直接系统测试
   */
  async runDirectTests() {
    console.log("🚀 开始执行直接系统测试");
    console.log("=".repeat(80));

    try {
      // 1. 初始化系统组件
      await this.initializeSystem();

      // 2. 测试双模型调用
      await this.testDualModelCalls();

      // 3. 测试信息收集触发机制
      await this.testInfoCollectionTriggers();

      // 4. 测试推荐系统链路
      await this.testRecommendationPipeline();

      // 5. 测试容错机制
      await this.testErrorHandling();

      // 6. 生成测试报告
      this.generateReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.logTestResult("SYSTEM_FAILURE", {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 初始化系统组件
   */
  async initializeSystem() {
    console.log("\n📊 初始化系统组件");
    console.log("-".repeat(50));

    const testCase = {
      name: "系统组件初始化",
      startTime: Date.now(),
    };

    try {
      // 1. 初始化配置
      this.config = new AppConfig();
      await this.config.initialize();
      console.log("✅ 配置初始化完成");

      // 2. 初始化数据库
      this.database = new DatabaseManager(this.config.getDatabaseConfig());
      await this.database.connect();
      console.log("✅ 数据库连接成功");

      // 3. 初始化用户管理器
      this.userManager = new UserManager(this.database, this.config);
      await this.userManager.initialize();
      console.log("✅ 用户管理器初始化完成");

      // 4. 初始化消息处理器
      this.messageProcessor = new MessageProcessor({
        database: this.database,
        userManager: this.userManager,
        config: this.config,
      });
      await this.messageProcessor.initialize();
      console.log("✅ 消息处理器初始化完成");

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";

      console.log(`📊 初始化耗时: ${testCase.responseTime}ms`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log("❌ 系统初始化失败");
      console.log(`💥 错误信息: ${error.message}`);
      throw error;
    }

    this.logTestResult("SYSTEM_INITIALIZATION", testCase);
  }

  /**
   * 测试双模型调用
   */
  async testDualModelCalls() {
    console.log("\n🤖 测试双模型调用");
    console.log("-".repeat(50));

    const testCases = [
      {
        name: "DeepSeek模型调用测试",
        message: "你好，我想找工作",
        userEmail: "<EMAIL>",
      },
      {
        name: "意图识别测试",
        message: "我是Java开发工程师，有5年经验",
        userEmail: "<EMAIL>",
      },
      {
        name: "信息提取测试",
        message: "我在阿里巴巴工作，P6级别，期望40k",
        userEmail: "<EMAIL>",
      },
    ];

    for (const testCase of testCases) {
      await this.executeDualModelTest(testCase);
    }
  }

  /**
   * 执行双模型测试
   */
  async executeDualModelTest(testCase) {
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`📝 输入消息: "${testCase.message}"`);

      // 直接调用消息处理器
      const response = await this.messageProcessor.processMessage({
        message: testCase.message,
        userEmail: testCase.userEmail,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.rawResponse = response;

      console.log(`✅ 测试通过: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 响应内容:`, JSON.stringify(response, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("DUAL_MODEL_CALL", testCase);
  }

  /**
   * 测试信息收集触发机制
   */
  async testInfoCollectionTriggers() {
    console.log("\n📋 测试信息收集触发机制");
    console.log("-".repeat(50));

    const triggerTests = [
      {
        name: "职位询问触发",
        message: "有什么职位推荐吗",
        expectedTrigger: true,
      },
      {
        name: "技术栈信息触发",
        message: "我是Python开发工程师",
        expectedTrigger: true,
      },
      {
        name: "无关内容测试",
        message: "今天天气真好",
        expectedTrigger: false,
      },
      {
        name: "空字符串测试",
        message: "",
        expectedTrigger: false,
      },
    ];

    for (const test of triggerTests) {
      await this.executeInfoCollectionTest(test);
    }
  }

  /**
   * 执行信息收集触发测试
   */
  async executeInfoCollectionTest(testCase) {
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行触发测试: ${testCase.name}`);
      console.log(`📝 输入内容: "${testCase.message}"`);

      const response = await this.messageProcessor.processMessage({
        message: testCase.message,
        userEmail: `trigger-test-${Date.now()}@example.com`,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.rawResponse = response;

      // 分析是否触发了信息收集
      const actualTrigger = this.analyzeInfoCollectionTrigger(response);
      testCase.actualTrigger = actualTrigger;
      testCase.triggerMatch = actualTrigger === testCase.expectedTrigger;

      if (testCase.triggerMatch) {
        console.log(
          `✅ 触发机制正确: 预期=${testCase.expectedTrigger}, 实际=${actualTrigger}`
        );
      } else {
        console.log(
          `❌ 触发机制错误: 预期=${testCase.expectedTrigger}, 实际=${actualTrigger}`
        );
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 触发测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("INFO_COLLECTION_TRIGGER", testCase);
  }

  /**
   * 分析信息收集触发情况
   */
  analyzeInfoCollectionTrigger(response) {
    if (!response.success || !response.response) {
      return false;
    }

    const content = response.response.content || "";
    const triggerKeywords = [
      "请告诉我",
      "能否告诉我",
      "您的技术",
      "工作经验",
      "期望薪资",
      "技术栈",
      "技术方向",
      "所在公司",
    ];

    return triggerKeywords.some((keyword) => content.includes(keyword));
  }

  /**
   * 测试推荐系统链路
   */
  async testRecommendationPipeline() {
    console.log("\n🎯 测试推荐系统链路");
    console.log("-".repeat(50));

    const pipelineTests = [
      {
        name: "完整信息推荐测试",
        message: "我是Python算法工程师，在阿里工作，P6级别，期望40k",
        userEmail: "<EMAIL>",
        expectedRecommendation: true,
      },
      {
        name: "部分信息测试",
        message: "我是Java开发，有3年经验",
        userEmail: "<EMAIL>",
        expectedRecommendation: false,
      },
      {
        name: "无效技术栈测试",
        message: "我是厨师，会做菜",
        userEmail: "<EMAIL>",
        expectedRecommendation: false,
      },
    ];

    for (const test of pipelineTests) {
      await this.executePipelineTest(test);
    }
  }

  /**
   * 执行推荐系统管道测试
   */
  async executePipelineTest(testCase) {
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行管道测试: ${testCase.name}`);
      console.log(`📝 输入消息: "${testCase.message}"`);

      const response = await this.messageProcessor.processMessage({
        message: testCase.message,
        userEmail: testCase.userEmail,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.rawResponse = response;

      // 分析推荐结果
      testCase.recommendationResult =
        this.analyzeRecommendationResult(response);

      console.log(`✅ 管道测试完成: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`🎯 推荐结果: ${testCase.recommendationResult}`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 管道测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("RECOMMENDATION_PIPELINE", testCase);
  }

  /**
   * 测试容错机制
   */
  async testErrorHandling() {
    console.log("\n🛡️ 测试容错机制");
    console.log("-".repeat(50));

    const errorTests = [
      {
        name: "空消息测试",
        message: null,
        userEmail: "<EMAIL>",
        expectedError: true,
      },
      {
        name: "超长消息测试",
        message: "A".repeat(10000),
        userEmail: "<EMAIL>",
        expectedError: false, // 应该被正常处理
      },
      {
        name: "特殊字符测试",
        message: '<script>alert("xss")</script>',
        userEmail: "<EMAIL>",
        expectedError: false, // 应该被正常处理
      },
    ];

    for (const test of errorTests) {
      await this.executeErrorTest(test);
    }
  }

  /**
   * 执行容错测试
   */
  async executeErrorTest(testCase) {
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行容错测试: ${testCase.name}`);
      console.log(
        `📝 输入参数:`,
        JSON.stringify(
          {
            message: testCase.message
              ? testCase.message.substring(0, 100) + "..."
              : testCase.message,
            userEmail: testCase.userEmail,
          },
          null,
          2
        )
      );

      const response = await this.messageProcessor.processMessage({
        message: testCase.message,
        userEmail: testCase.userEmail,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.rawResponse = response;

      // 分析错误处理结果
      const isError = !response.success;
      testCase.actualError = isError;
      testCase.errorHandlingCorrect = isError === testCase.expectedError;

      if (testCase.errorHandlingCorrect) {
        testCase.status = "SUCCESS";
        console.log(`✅ 容错测试通过: ${testCase.name}`);
        console.log(
          `🎯 错误处理正确: 预期=${testCase.expectedError}, 实际=${isError}`
        );
      } else {
        testCase.status = "FAILURE";
        console.log(`❌ 容错测试失败: ${testCase.name}`);
        console.log(
          `🎯 错误处理错误: 预期=${testCase.expectedError}, 实际=${isError}`
        );
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "NETWORK_ERROR";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 容错测试异常: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("ERROR_HANDLING", testCase);
  }

  /**
   * 分析推荐结果
   */
  analyzeRecommendationResult(response) {
    if (!response.success || !response.response) {
      return "NO_RECOMMENDATION";
    }

    const content = response.response.content || "";
    const recommendationKeywords = ["推荐", "职位", "岗位", "公司", "机会"];

    return recommendationKeywords.some((keyword) => content.includes(keyword))
      ? "RECOMMENDED"
      : "NOT_RECOMMENDED";
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log("\n" + "=".repeat(80));
    console.log("📊 直接系统测试 - 详细报告");
    console.log("=".repeat(80));

    const totalTime = Date.now() - this.startTime;
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(
      (t) => t.status === "SUCCESS"
    ).length;
    const failedTests = this.testResults.filter(
      (t) => t.status !== "SUCCESS"
    ).length;
    const successRate =
      totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(2) : 0;

    console.log(`\n📈 总体测试统计:`);
    console.log(
      `⏰ 总测试时间: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}秒)`
    );
    console.log(`🧪 总测试数量: ${totalTests}`);
    console.log(`✅ 成功测试: ${successfulTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📊 成功率: ${successRate}%`);

    // 按类别统计
    const categoryStats = {};
    this.testResults.forEach((test) => {
      if (!categoryStats[test.category]) {
        categoryStats[test.category] = { total: 0, success: 0, failure: 0 };
      }
      categoryStats[test.category].total++;
      if (test.status === "SUCCESS") {
        categoryStats[test.category].success++;
      } else {
        categoryStats[test.category].failure++;
      }
    });

    console.log(`\n📋 分类测试统计:`);
    Object.entries(categoryStats).forEach(([category, stats]) => {
      const categorySuccessRate = ((stats.success / stats.total) * 100).toFixed(
        2
      );
      console.log(
        `  ${category}: ${stats.success}/${stats.total} (${categorySuccessRate}%)`
      );
    });

    // 详细测试结果
    console.log(`\n📝 详细测试结果:`);
    this.testResults.forEach((test, index) => {
      console.log(
        `\n${index + 1}. ${test.category} - ${test.name || "未命名测试"}`
      );
      console.log(`   状态: ${test.status}`);
      console.log(`   时间: ${test.responseTime || "N/A"}ms`);

      if (test.status === "SUCCESS") {
        console.log(`   ✅ 测试通过`);
      } else {
        console.log(`   ❌ 测试失败: ${test.error || "未知错误"}`);
      }

      // 显示关键测试数据
      if (test.rawResponse) {
        const responsePreview = JSON.stringify(test.rawResponse).substring(
          0,
          100
        );
        console.log(`   响应: ${responsePreview}...`);
      }
    });

    console.log("\n" + "=".repeat(80));
    console.log("📊 测试报告生成完成");
    console.log("=".repeat(80));
  }

  /**
   * 记录测试结果
   */
  logTestResult(category, testCase) {
    this.testResults.push({
      category,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new DirectSystemTester();
  tester.runDirectTests().catch(console.error);
}

module.exports = DirectSystemTester;
