# 数据库结构参考手册

**生成时间**: 2025 年 7 月 27 日
**用途**: 开发时快速查阅数据库表结构，避免字段名错误

---

## 🔍 核心表结构

### 1. users (用户表)

- **记录数**: 379 ⬆️ (增长 166%)
- **字段**:
  - `id` (integer) - 主键
  - `email` (text) - 邮箱
  - `user_type` (text) - 用户类型
  - `created_at` (timestamp) - 创建时间
  - `last_login_at` (nullable) - 最后登录时间
  - `is_active` (boolean) - 是否激活

### 2. chat_sessions (会话表)

- **记录数**: 566 ⬆️ (增长 153%)
- **字段**:
  - `id` (integer) - 主键
  - `user_id` (integer) - 用户 ID
  - `session_uuid` (uuid) - 会话 UUID（外部标识）
  - `entry_source_url` (text) - 入口来源
  - `initial_intent` (text) - 初始意图
  - `current_interaction_context` (json) - 当前交互上下文
  - `last_active_at` (timestamp) - 最后活跃时间
  - `created_at` (timestamp) - 创建时间

### 3. chat_messages (消息表) ⭐ 重要

- **记录数**: 2618 ⬆️ (增长 135%)
- **字段**:
  - `id` (integer) - 主键
  - `session_id` (integer) - 会话 ID（关联 chat_sessions.id）
  - `message_type` (text) - 消息类型（user/assistant）
  - `message_content` (text) - 消息内容
  - `metadata_json` (json) - 元数据
  - `timestamp` (timestamp) - 时间戳

### 4. candidate_profiles (候选人档案表)

- **记录数**: 157 ⬆️ (增长 99%)
- **关键字段**:
  - `id`, `user_id`
  - `current_company_name_raw`, `current_company_id`
  - `candidate_level_raw`, `candidate_standard_level_min/max`
  - `primary_tech_direction_id`, `candidate_tech_direction_raw`
  - `primary_business_scenario_id`, `candidate_business_scenario_raw` 🆕
  - `expected_compensation_min/max`
  - `profile_completeness_score`

### 5. companies (公司表)

- **记录数**: 79
- **字段**:
  - `id`, `company_name`, `company_type`
  - `industry`, `description`, `website`
  - `logo_url`, `is_blocked`

### 6. job_listings (职位表)

- **记录数**: 50 (稳定)
- **关键字段**:
  - `id`, `job_title`, `company_id`
  - `salary_min/max`, `experience_required`
  - `primary_tech_direction_id`
  - `primary_business_scenario_id` 🆕
  - `job_level_raw`, `job_standard_level_min/max`
  - `job_description`, `requirements`, `benefits`
  - `is_active`, `priority_level`

### 7. tech_tree (技术树表) 🔥 核心

- **记录数**: 648 (稳定)
- **层级结构**: 3 级技术树 (35 个 1 级 + 165 个 2 级 + 448 个 3 级)
- **字段**:
  - `id`, `tech_name`, `category`
  - `parent_tech_id`, `level`
  - `description`, `keywords`

### 8. business_scenarios (业务场景表) 🔥 核心

- **记录数**: 115 ⬆️ (增长 423%)
- **层级结构**: 3 级业务场景 (15 个 1 级 + 30 个 2 级 + 70 个 3 级)
- **字段**:
  - `id`, `scenario_name`, `category`
  - `parent_scenario_id`, `level`
  - `description`, `keywords`, `business_value`

---

## 🔧 常用查询模式

### 获取对话历史

```sql
SELECT * FROM chat_messages
WHERE session_id = ?
ORDER BY timestamp DESC
LIMIT 10;
```

### 获取会话信息

```sql
SELECT * FROM chat_sessions
WHERE session_uuid = ?;
```

### 查询匹配职位

```sql
SELECT jl.*, c.company_name, c.company_type
FROM job_listings jl
JOIN companies c ON jl.company_id = c.id
WHERE jl.primary_tech_direction_id = ?
AND jl.is_active = true;
```

---

## ⚠️ 重要注意事项

1. **会话管理**:

   - 外部使用 `session_uuid` (UUID 格式)
   - 内部关联使用 `session_id` (整数)

2. **消息存储**:

   - 使用 `session_id` 而不是 `user_id`
   - `metadata_json` 存储结构化数据

3. **字段命名**:

   - 数据库使用下划线命名 (`session_id`)
   - 不要使用驼峰命名 (`sessionId`)

4. **常见错误**:
   - ❌ `chat_messages.user_id` (不存在)
   - ✅ `chat_messages.session_id` (正确)
   - ❌ `TABLES.CHAT_MESSAGES` (可能过时)
   - ✅ `'chat_messages'` (直接使用表名)

---

## 📊 数据统计 (更新于 2025-07-27)

| 表名               | 记录数 | 变化     | 状态 |
| ------------------ | ------ | -------- | ---- |
| users              | 379    | ⬆️ +236  | ✅   |
| chat_sessions      | 566    | ⬆️ +342  | ✅   |
| chat_messages      | 2618   | ⬆️ +1504 | ✅   |
| candidate_profiles | 157    | ⬆️ +78   | ✅   |
| companies          | 79     | ➡️ 稳定  | ✅   |
| job_listings       | 50     | ➡️ 稳定  | ✅   |
| tech_tree          | 648    | ➡️ 稳定  | ✅   |
| business_scenarios | 115    | ⬆️ +93   | ✅   |

## 🆕 新增功能

### 业务场景映射系统

- **job_listings** 表新增 `primary_business_scenario_id` 字段
- **candidate_profiles** 表新增 `primary_business_scenario_id` 和 `candidate_business_scenario_raw` 字段
- **business_scenarios** 表扩展为 115 个场景，支持 3 级层级结构

---

## 🎯 开发建议

1. **优先使用实际字段名**，避免依赖常量文件
2. **会话管理**已修复，可以进行连续对话测试
3. **数据库结构稳定**，可以开始完整功能测试
4. **字段映射**参考上面的结构，避免查询错误

---

_此文件基于实际数据库结构生成，请在开发时优先参考此文档_

---

## 🌳 技术栈映射表 (tech_tree) - 详细分析

### 数据概览：

- **总记录数**: 648 条技术记录
- **层级分布**: Level 1(35 个) → Level 2(约 200 个) → Level 3(约 400 个)
- **关键字段**: `id`, `tech_name`, `level`, `parent_id`, `keywords`

### Level 1 - 主要技术方向 (35 个)：

#### 算法类技术方向：

- **ID:725** - 推荐算法
- **ID:726** - 广告算法
- **ID:727** - 搜索算法
- **ID:728** - CV 算法（计算机视觉）
- **ID:729** - NLP 算法（自然语言处理）
- **ID:730** - 多模态算法 ⭐
- **ID:731** - 大模型（LLM）算法
- **ID:732** - 语音算法
- **ID:733** - 视频算法
- **ID:734** - 通用机器学习/深度学习算法
- **ID:735** - 图神经网络/图算法
- **ID:736** - 联邦学习/隐私计算
- **ID:737** - 增强学习（RL）算法
- **ID:738** - AIGC 生成算法
- **ID:739** - OCR/文档理解算法
- **ID:740** - 智能问答/Agent 算法
- **ID:741** - 自动驾驶/无人驾驶算法

#### 行业专业算法：

- **ID:742** - 金融风控算法
- **ID:743** - 生物医疗算法
- **ID:744** - 工业/IoT 算法
- **ID:748** - 电商算法
- **ID:749** - 智能风控/反作弊算法
- **ID:750** - 智能物流/调度算法

#### 应用场景算法：

- **ID:745** - 搜索推荐结合算法
- **ID:746** - 智能客服/智能对话算法
- **ID:747** - 游戏 AI 算法
- **ID:751** - 图像生成/3D 重建算法
- **ID:752** - 边缘计算/设备端算法
- **ID:753** - 时序预测算法
- **ID:754** - 运营分析算法
- **ID:755** - 教育 AI/个性化学习算法
- **ID:756** - 智能制造/质检算法
- **ID:757** - 智能硬件算法
- **ID:758** - 数据挖掘/分析算法
- **ID:759** - 算法策略岗（优化方向）

### Level 2 - 技术分类示例：

- **召回策略** (ID:760)
- **排序优化** (ID:761)
- **特征工程与 Embedding 建模** (ID:762)
- **在线/离线推荐系统工程** (ID:763)
- **竞价策略建模** (ID:765)
- **点击/转化率预估模型** (ID:766)
- **Query 理解与改写** (ID:770)

### Level 3 - 具体技术示例：

- **用户 Embedding** (ID:942)
- **商品/物品 Embedding** (ID:943)
- **多塔结构建模** (ID:944)
- **序列建模** (ID:945)
- **实时推荐系统** (ID:947)
- **向量检索（ANN）** (ID:950)
- **RTB 竞价优化** (ID:954)
- **GBDT+LR 模型** (ID:958)
- **DeepFM 模型** (ID:959)
- **DIN 结构** (ID:960)

### 歧义识别关键映射：

#### 常见用户表达 → 技术方向映射：

```javascript
const techMappingRules = {
  // 多模态相关
  多模态: [730], // 多模态算法
  图像: [728, 730, 751], // CV算法、多模态算法、图像生成
  视觉: [728, 730], // CV算法、多模态算法

  // 算法通用词
  算法: [734], // 通用机器学习算法（默认）
  AI: [734, 740], // 通用ML、智能问答
  机器学习: [734], // 通用机器学习算法

  // 具体方向
  推荐: [725], // 推荐算法
  搜索: [727], // 搜索算法
  NLP: [729], // NLP算法
  大模型: [731], // 大模型算法
  自动驾驶: [741], // 自动驾驶算法
};
```

---

## 🏢 业务场景映射表 (business_scenarios) - 详细分析

### 数据概览：

- **总记录数**: 115 条业务场景记录
- **层级分布**: Level 1(15 个) → Level 2(约 30 个) → Level 3(约 70 个)
- **关键字段**: `id`, `scenario_name`, `level`, `parent_scenario_id`, `keywords`

### Level 1 - 主要业务领域 (15 个)：

- **ID:2000** - 电商零售
  - 关键词: {电商,零售,网购,购物,商城,淘宝,京东,拼多多,天猫,网店}
- **ID:2001** - 金融服务
  - 关键词: {金融,银行,保险,投资,理财,贷款,支付,证券,基金,信贷}
- **ID:2002** - 人工智能 ⭐
  - 关键词: {AI,人工智能,机器学习,深度学习,算法,智能,自动化,机器人,神经网络}
- **ID:2003** - 教育培训
  - 关键词: {教育,培训,学习,课程,在线教育,知识付费,技能培训,学校,老师}
- **ID:2004** - 医疗健康
  - 关键词: {医疗,健康,医院,医生,药品,诊断,治疗,体检,养生,保健}
- **ID:2005** - 物流运输
  - 关键词: {物流,运输,快递,配送,仓储,供应链,货运,邮寄,发货}
- **ID:2006** - 房地产
  - 关键词: {房地产,房产,买房,租房,装修,建筑,地产,楼盘,中介}
- **ID:2007** - 旅游出行
  - 关键词: {旅游,出行,酒店,机票,景点,度假,民宿,自驾,攻略}
- **ID:2008** - 餐饮美食
  - 关键词: {餐饮,美食,外卖,餐厅,菜谱,烹饪,食材,饮品,小吃}
- **ID:2009** - 娱乐传媒
  - 关键词: {娱乐,传媒,游戏,视频,音乐,直播,社交,内容,媒体}
- **ID:2010** - 企业服务
  - 关键词: {企业服务,SaaS,办公,管理,协作,云服务,CRM,ERP,工具}
- **ID:2011** - 制造业
  - 关键词: {制造,生产,工厂,加工,制造业,工业,设备,自动化,质量控制}
- **ID:2012** - 农业
  - 关键词: {农业,种植,养殖,农产品,农场,农民,农村,绿色,有机}
- **ID:2013** - 能源环保
  - 关键词: {能源,环保,新能源,节能,绿色,清洁,可再生,环境,碳中和}
- **ID:2014** - 政府公共
  - 关键词: {政府,公共服务,政务,民生,社会,公益,便民,数字政府}

### Level 2 - 业务分类示例：

- **ID:2100** - 平台电商 (父级: 电商零售)
  - 关键词: {平台电商,电商平台,多商户,入驻,佣金,流量分发}

### 歧义识别关键映射：

#### 常见用户表达 → 业务场景映射：

```javascript
const businessMappingRules = {
  // 电商相关
  电商: [2000], // 电商零售
  跨境电商: [2100], // 平台电商（需要进一步细分）
  购物: [2000], // 电商零售

  // 金融相关
  金融: [2001], // 金融服务
  银行: [2001], // 金融服务
  支付: [2001], // 金融服务

  // AI相关
  AI: [2002], // 人工智能
  人工智能: [2002], // 人工智能
  算法: [2002], // 人工智能（业务场景）

  // 其他常见场景
  教育: [2003], // 教育培训
  医疗: [2004], // 医疗健康
  物流: [2005], // 物流运输
  游戏: [2009], // 娱乐传媒
};
```

---

## 💼 职位推荐关联分析

### job_listings 表关键字段：

- `primary_tech_direction_id` - 主要技术方向 ID（关联 tech_tree）
- `primary_business_scenario_id` - 主要业务场景 ID（关联 business_scenarios）
- `job_standard_level_min/max` - 标准职级范围
- `salary_min/max` - 薪资范围
- `company_id` - 公司 ID（关联 companies）

### 职位推荐示例分析：

1. **多模态算法专家** (得物)

   - 技术方向: ID:730 (多模态算法)
   - 业务场景: ID:2002 (人工智能)
   - 职级: P7 (标准职级 7)
   - 薪资: 100-150K

2. **大模型算法专家** (阿里巴巴)
   - 技术方向: ID:729 (NLP 算法)
   - 业务场景: ID:2126 (具体业务场景)
   - 职级: P7 (标准职级 7)
   - 薪资: 100-150K

### 推荐匹配逻辑：

```javascript
const jobMatchingCriteria = {
  // 必须匹配
  techDirection: "primary_tech_direction_id",

  // 优先匹配
  businessScenario: "primary_business_scenario_id",
  salaryRange: "salary_min/max",
  jobLevel: "job_standard_level_min/max",

  // 加分项
  companyType: "companies.company_type",
  location: "job_location",
};
```

---

## 👤 候选人档案表 (candidate_profiles) - 关键字段

### 表结构：

```sql
candidate_profiles {
  id                              -- 主键
  user_id                         -- 用户ID（关联users表）
  current_company_name_raw        -- 当前公司名称（原始输入）
  current_company_id              -- 当前公司ID（关联companies表）
  candidate_level_raw             -- 候选人职级（原始输入）
  candidate_standard_level_min    -- 标准职级最小值
  candidate_standard_level_max    -- 标准职级最大值
  primary_tech_direction_id       -- 主要技术方向ID（关联tech_tree表）
  candidate_tech_direction_raw    -- 技术方向（原始输入）
  primary_business_scenario_id    -- 主要业务场景ID（关联business_scenarios表）
  candidate_business_scenario_raw -- 业务场景（原始输入）
  desired_location_raw            -- 期望城市（原始输入）
  desired_location_standard       -- 标准化城市
  expected_compensation_raw       -- 期望薪资（原始输入）
  expected_compensation_min       -- 期望薪资最小值
  expected_compensation_max       -- 期望薪资最大值
  profile_completeness_score      -- 档案完整度评分
  last_updated_at                 -- 最后更新时间
  created_at                      -- 创建时间
}
```

### 用户信息映射关系：

```javascript
const userInfoMapping = {
  // 前端显示名称 → 数据库字段名
  所在公司: "current_company_name_raw",
  技术方向: "candidate_tech_direction_raw",
  当前职级: "candidate_level_raw",
  期望薪资: "expected_compensation_raw",
  所在城市: "desired_location_raw",
  业务场景: "candidate_business_scenario_raw",
};
```

---

## 💼 职位列表表 (job_listings) - 关键字段

### 表结构：

```sql
job_listings {
  id                            -- 主键
  job_title                     -- 职位名称
  company_id                    -- 公司ID（关联companies表）
  department                    -- 部门
  location                      -- 工作地点
  salary_min                    -- 最低薪资
  salary_max                    -- 最高薪资
  experience_required           -- 经验要求
  primary_tech_direction_id     -- 主要技术方向ID（关联tech_tree表）
  job_level_raw                 -- 职级描述（原始）
  job_standard_level_min        -- 标准职级最小值
  job_standard_level_max        -- 标准职级最大值
  primary_business_scenario_id  -- 主要业务场景ID（关联business_scenarios表）
  job_description               -- 职位描述
  requirements                  -- 职位要求
  benefits                      -- 福利待遇
  contact_info                  -- 联系信息
  is_active                     -- 是否激活
  priority_level                -- 优先级
  created_at                    -- 创建时间
  updated_at                    -- 更新时间
}
```

### 职位推荐查询逻辑：

```javascript
const jobQueryCriteria = {
  // 必须匹配字段
  primary_tech_direction_id: "技术方向ID",
  is_active: true,

  // 筛选条件
  company_type: "公司类型（通过companies表关联）",
  salary_range: "salary_min/salary_max",
  job_level_range: "job_standard_level_min/max",

  // 排序
  priority_level: "DESC",
};
```

---

## 🏢 公司信息表 (companies) - 关键字段

### 表结构：

```sql
companies {
  id            -- 主键
  company_name  -- 公司名称
  company_type  -- 公司类型（头部大厂、中型公司、国企、创业型公司）
  industry      -- 行业
  description   -- 公司描述
  website       -- 官网
  logo_url      -- Logo地址
  is_blocked    -- 是否屏蔽
  created_at    -- 创建时间
}
```

### 4\*4 推荐逻辑的公司类型映射：

```javascript
const companyTypeMapping = {
  头部大厂: ["腾讯", "阿里巴巴", "字节跳动", "美团", "百度", "京东"],
  中型公司: ["得物", "知乎", "小红书", "B站"],
  国企: ["中兴通讯", "中国移动", "国家电网"],
  创业型公司: ["小虾米公司", "小卡拉米公司"],
};
```

---

## 🏢 公司信息表 (companies)

### 实际数据库公司列表（79 家公司）：

#### 头部大厂：

- **腾讯** (别名: 鹅厂、TX)
- **阿里巴巴** (别名: 阿里、Ali)
- **字节跳动** (别名: 字节、BD)
- **美团** (别名: 美团、MT)
- **蚂蚁金服** (别名: 蚂蚁、支付宝)
- **高德地图** (别名: 高德)
- **钉钉** (别名: 钉钉)

#### 中型公司：

- **百度、京东、快手、小红书、知乎、哔哩哔哩（B 站）、得物、滴滴、饿了么、荣耀**
- **科大讯飞、四维图新、深圳力合微电子、马上消费金融、顺丰科技**
- **SHEIN、Bigo、传音控股、哈啰出行、联影中央研究院、蓝色光标**

#### 外资大厂：

- **微软** (别名: 微软、MS)
- **谷歌** (别名: 谷歌、Google)
- **亚马逊** (别名: 亚麻、Amazon)
- **Meta/Facebook** (别名: Meta、脸书)

#### 创业型公司：

- **优地机器人、九识智能、坎德拉科技、赢彻科技、松灵机器人**
- **北京卓鸷科技、北京唯实具身智能研究院、西部智联科技**
- **易控驾智科技、墨芯人工智能科技、三个逗号**

#### 国企：

- **中兴通讯、徐工集团、宁波银行、重庆长安工业集团**
- **中国科学院空间应用工程与技术中心、沈阳机器人产业发展集团**

---

## 📈 职级对照表 (candidate_standard_levels)

### 实际数据库职级映射（96 条记录）：

#### 阿里系（标准参考）：

- **P4**: 初级工程师 (1-3 年经验, 80-120 万)
- **P5**: 工程师 (2-4 年经验, 100-150 万)
- **P6**: 高级工程师 (4-7 年经验, 150-250 万)
- **P7**: 专家工程师 (6-9 年经验, 200-300 万)
- **P8**: 资深专家 (8-11 年经验, 250-350 万)
- **P9**: 首席专家 (10-13 年经验, 300-400 万)
- **P10**: 技术总监 (12-15 年经验, 350-450 万)

#### 腾讯系：

- **8 级**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **9 级**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **9 级高级**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **10 级**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **11 级**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **12 级**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **13 级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **14 级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **15 级**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 字节系：

- **2-1**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **2-2**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **3-1**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **3-2**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **3-2 高级**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **4-1**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **4-2**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 百度系：

- **T4/T5**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **T5 高级/T6**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **T6 高级/T7**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **T7 高级/T8**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **T9/T10**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 美团系：

- **L6/L7**: 对应阿里 P6 (4-7 年经验, 150-250 万)
- **L7 高级/L8**: 对应阿里 P7 (6-9 年经验, 200-300 万)
- **L9**: 对应阿里 P8 (8-11 年经验, 250-350 万)
- **L10**: 对应阿里 P9 (10-13 年经验, 300-400 万)
- **L10+**: 对应阿里 P10 (12-15 年经验, 350-450 万)

#### 外资大厂：

- **微软**: 60 级(P6) → 61-62 级(P7) → 64-65 级(P8) → 65 高级-66 级(P9) → 67 级+(P10)
- **谷歌**: L3-L4(P7) → L5(P8) → L6(P9) → L7+(P10)
- **亚马逊**: L4-L5(P7) → L6+(P8-P10)
- **Meta**: E3-E4(P7) → E4 高级(P8) → E5(P9) → E6+(P10)

---

## 📊 实际数据库查询结果 (2025-07-27)

### 🌳 tech_tree 表实际数据：

- **总记录数**: 648 条技术记录
- **层级分布**: Level 1(35 条), Level 2(约 200 条), Level 3(约 400 条)
- **字段结构**: id, tech_name, level, parent_id 等字段确认
- **数据完整性**: 技术树数据完整，支持父子级关系查询

#### Level 1 主要技术方向（35 个）：

- **算法类**: 推荐算法、广告算法、搜索算法、CV 算法、NLP 算法、多模态算法
- **大模型**: 大模型（LLM）算法、语音算法、视频算法
- **机器学习**: 通用机器学习/深度学习算法、图神经网络/图算法
- **开发类**: 前端开发、后端开发、移动端开发、全栈开发
- **基础设施**: 数据工程、云计算、DevOps、测试工程
- **产品类**: 产品经理、设计师、运营

### 🏢 companies 表实际数据：

- **总记录数**: 约 50+家公司
- **公司类型**: 头部大厂、中型公司、创业型公司等
- **实际公司**: 字节跳动、腾讯、阿里巴巴、百度等主流科技公司
- **字段**: company_name, company_type, industry 等

### 📈 职级数据状态：

- **candidate_standard_levels 表**: 未找到或无权限访问
- **替代方案**: 需要通过其他表或字段获取职级信息
- **建议**: 使用通用职级映射或从 candidate_profiles 表获取

### 🔍 其他关键表：

- **job_listings**: 50 条职位记录
- **candidate_profiles**: 157 条候选人档案
- **business_scenarios**: 115 条业务场景数据

### 💡 开发建议：

1. **技术映射**: 直接查询 tech_tree 表，支持三级层级匹配
2. **公司识别**: 基于 companies 表的真实数据进行匹配
3. **职级处理**: 使用通用职级体系或从档案表提取
4. **数据缓存**: 考虑缓存常用的技术和公司数据以提升性能

---

## 📋 完整映射数据参考

### 🏢 公司类型映射 (companies 表 - 79 条记录)

#### 头部大厂 (7 家)

- 阿里巴巴 (ID:260) - 电商、云计算、AI、大模型、物流、金融科技
- 腾讯 (ID:261) - 社交、游戏、金融、云服务、AI、大模型
- 滴滴 (ID:262) - 网约车、自动驾驶、智慧交通、AI 平台
- 美团 (ID:271) - 外卖、到店、闪购、智能配送、无人车
- 蚂蚁金服 (ID:272) - 支付宝、区块链、AI 风控、智能投顾
- 字节跳动 (ID:305) - 内容平台、AI 大模型、教育科技、办公协作
- 高德地图 (ID:267) - 智能导航、交通大脑、语音助手、智慧出行

#### 中型公司 (28 家)

- 小红书 (ID:269) - 内容种草、电商、广告、搜索推荐
- 得物 (ID:268) - 鉴别服务、电商交易、社区内容、AI 识别
- 知乎 (ID:263) - 问答社区、内容付费、广告、企业服务
- 饿了么 (ID:281) - 外卖配送、即时零售、本地服务
- 哔哩哔哩 (ID:314) - 视频弹幕社区、直播、内容推荐、大模型
- 科大讯飞 (ID:294) - 语音识别、教育 AI、智能办公、医疗 AI
- 荣耀 (ID:289) - 手机、笔记本、AI 操作系统、智能硬件
- SHEIN (ID:279) - 跨境电商、商品上新、AI 图像生成、推荐系统
- 哈啰出行 (ID:276) - 共享单车、电单车、顺风车、AI 智能调度
- 58 同城 (ID:270) - 招聘、租房、二手、本地服务
- Bigo (ID:266) - 直播、短视频、社交娱乐、AI 推荐
- 顺丰科技 (ID:302) - 智能物流、自动配送、物流大脑
- 马上消费金融 (ID:290) - 消费信贷、智能风控、数字支付
- 蓝色光标 (ID:327) - 智能营销、用户洞察、AI 内容创意
- 联影中央研究院 (ID:326) - 医学影像识别、AI 诊断、临床辅助决策
- 传音控股 (ID:334) - 手机、耳机、移动设备
- 四维图新 (ID:297) - 高精地图、车联网、导航引擎
- 天翼交通科技 (ID:291) - 城市交通调度、智慧出行、信号控制系统
- 童心制物科技 (ID:292) - 教育机器人、STEAM 教育、图形编程
- 纵维立方科技 (ID:284) - 桌面级 3D 打印机、AI 智能切片、工业应用
- 深圳力合微电子 (ID:306) - 物联网通信芯片、电力载波通信
- 深圳市道通智能航空技术 (ID:295) - 工业无人机、三维重建、巡检系统
- 北方华创微电子装备 (ID:307) - 刻蚀机、PVD、CVD、清洗设备
- 南威软件 (ID:303) - 智慧政务、城市管理平台、AI 治理方案
- 美资新能源企业 (ID:300) - 储能系统、新能源电池、智慧能源管理
- 龙旗控股 (ID:299) - ODM 智能终端、穿戴设备、IoT 产品开发
- 某电商独角兽 (ID:316) - 商品推荐、内容电商、社交营销
- 优酷 (ID:288) - 影视播放、内容制作、视频推荐系统

#### 国企 (8 家)

- 中兴通讯 (ID:278) - 通信设备、5G、AI 网络优化、云基础设施
- 中国科学院空间应用工程与技术中心 (ID:282) - 空间应用系统研发、航天实验管理
- 宁波银行 (ID:338) - 公司金融、零售银行、财富管理
- 徐工集团 (ID:277) - 重工设备、工业机器人、AI 远程控制
- 沈阳机器人产业发展集团 (ID:337) - 机器人制造、技术孵化、产业基金
- 重庆长安工业集团 (ID:322) - 工业制造、AI 智造、车联网
- 重庆长安新能源汽车科技 (ID:321) - 新能源汽车研发、车载智能系统
- 钉钉 (ID:280) - IM、OA 协作、视频会议、办公自动化

#### 创业型公司 (36 家)

- 思睿明智 (ID:265) - 工业视觉检测、智能制造、AI 算法平台
- 识货 (ID:264) - 球鞋、美妆、电商分发、社区内容
- 发现力量 (ID:273) - AI 投资孵化、产业智能体平台
- 北京心影随形科技 (ID:274) - AI 视频创作、实时驱动动画、图文转视频
- 重庆赛迪奇智人工智能科技 (ID:275) - 智慧政务、AI 城市中枢、知识图谱
- 九识智能 (ID:285) - 智能视频分析、边缘 AI、零售监控
- 优地机器人 (ID:286) - 商业配送机器人、巡检机器人、AI 语音交互
- 北京卓鸷科技 (ID:287) - 自动驾驶感知系统、数据闭环平台
- 百融云创 (ID:283) - 智能风控、反欺诈、智能催收、AI 客服
- 北京唯实具身智能研究院 (ID:293) - 多模态机器人、具身智能、大模型基础研究
- 北京轻松怡康信息技术 (ID:296) - 智能健康评估系统、AI 问诊、健康大数据
- 西部智联科技 (ID:298) - 城市大脑、AI 交通感知、工业智能决策
- 北京与爱为舞科技 (ID:301) - 虚拟偶像、AI 主播、内容生成平台
- 易控驾智科技 (ID:304) - 高精地图、自动驾驶算法、仿真系统
- 坎德拉科技 (ID:308) - 医疗数据标注、AI 影像辅助系统、LLM+诊断
- 赢彻科技 (ID:309) - 干线无人驾驶卡车、智能调度平台
- 深圳和而泰 (ID:310) - 智能家居控制面板、AIoT 终端
- Cettire 上海 (ID:311) - 跨境电商平台、时尚内容推荐
- Plus AI 苏州智加 (ID:312) - L4 无人驾驶卡车、视觉规划系统
- 广智网络 (ID:313) - 社区团购、生活服务推荐
- 成都精灵云 (ID:315) - 云平台、大数据、AI 大模型服务
- 松灵机器人 (ID:317) - 送餐机器人、巡检机器人、AI 语音交互
- 三个逗号 (ID:318) - 虚拟角色、语音交互、内容生成
- 洋钱罐 (ID:319) - 分期消费、AI 风控、信贷审批
- XREAL 优奈柯恩 (ID:320) - AR 智能眼镜、多模态交互、AI 助手
- 星宸科技 (ID:323) - 教育机器人、AI 教学平台
- 智慧互通科技 (ID:324) - 交通信号、智慧停车、AI 调度
- 钛虎机器人科技 (ID:325) - 巡检机器人、交互机器人、AI 自主导航
- 上海卡方信息科技 (ID:328) - 金融身份识别、反欺诈、AI 验证
- 帝奥微电子 (ID:329) - 电源管理芯片、模拟信号处理、汽车电子
- 墨芯人工智能科技 (ID:330) - 大模型推理芯片、AI 加速器、边缘计算
- 北京索英电气 (ID:331) - 储能系统、微电网、能源管理系统
- 立得空间信息技术 (ID:332) - 三维地图、数字孪生、遥感与测绘
- 深圳大漠大智控技术 (ID:333) - 智能跟随机器人、电机控制系统、机器人底盘
- 航天时代飞鹏 (ID:335) - 无人机、飞控系统、军民融合航空技术
- 天润融通 (ID:336) - 智能客服、AI 语音、企业通讯中台

### 🔧 技术方向映射 (tech_tree 表 - Level 1 技术方向 35 个)

#### 算法大类 (35 个技术方向)

- 推荐算法 (ID:725) - 关键词: 推荐算法
- 广告算法 (ID:726) - 关键词: 广告算法
- 搜索算法 (ID:727) - 关键词: 搜索算法
- CV 算法（计算机视觉） (ID:728) - 关键词: CV 算法（计算机视觉）
- NLP 算法（自然语言处理） (ID:729) - 关键词: NLP 算法（自然语言处理）
- 多模态算法 (ID:730) - 关键词: 多模态算法
- 大模型（LLM）算法 (ID:731) - 关键词: 大模型（LLM）算法
- 语音算法 (ID:732) - 关键词: 语音算法
- 视频算法 (ID:733) - 关键词: 视频算法
- 通用机器学习/深度学习算法 (ID:734) - 关键词: 通用机器学习/深度学习算法
- 图神经网络/图算法 (ID:735) - 关键词: 图神经网络/图算法
- 联邦学习/隐私计算 (ID:736) - 关键词: 联邦学习/隐私计算
- 增强学习（RL）算法 (ID:737) - 关键词: 增强学习（RL）算法
- AIGC 生成算法 (ID:738) - 关键词: AIGC 生成算法
- OCR/文档理解算法 (ID:739) - 关键词: OCR/文档理解算法
- 智能问答/Agent 算法 (ID:740) - 关键词: 智能问答/Agent 算法
- 自动驾驶/无人驾驶算法 (ID:741) - 关键词: 自动驾驶/无人驾驶算法
- 金融风控算法 (ID:742) - 关键词: 金融风控算法
- 生物医疗算法 (ID:743) - 关键词: 生物医疗算法
- 工业/IoT 算法 (ID:744) - 关键词: 工业/IoT 算法
- 搜索推荐结合算法 (ID:745) - 关键词: 搜索推荐结合算法
- 智能客服/智能对话算法 (ID:746) - 关键词: 智能客服/智能对话算法
- 游戏 AI 算法 (ID:747) - 关键词: 游戏 AI 算法
- 电商算法 (ID:748) - 关键词: 电商算法
- 智能风控/反作弊算法 (ID:749) - 关键词: 智能风控/反作弊算法
- 智能物流/调度算法 (ID:750) - 关键词: 智能物流/调度算法
- 图像生成/3D 重建算法 (ID:751) - 关键词: 图像生成/3D 重建算法
- 边缘计算/设备端算法 (ID:752) - 关键词: 边缘计算/设备端算法
- 时序预测算法 (ID:753) - 关键词: 时序预测算法
- 运营分析算法 (ID:754) - 关键词: 运营分析算法
- 教育 AI/个性化学习算法 (ID:755) - 关键词: 教育 AI/个性化学习算法
- 智能制造/质检算法 (ID:756) - 关键词: 智能制造/质检算法
- 智能硬件算法 (ID:757) - 关键词: 智能硬件算法
- 数据挖掘/分析算法 (ID:758) - 关键词: 数据挖掘/分析算法
- 算法策略岗（优化方向） (ID:759) - 关键词: 算法策略岗（优化方向）

### 🏭 业务场景映射 (business_scenarios 表 - Level 1 业务场景 15 个)

#### 行业大类 (15 个业务场景)

- 电商零售 (ID:2000) - 关键词: {电商,零售,网购,购物,商城,淘宝,京东,拼多多,天猫,网店}
- 金融服务 (ID:2001) - 关键词: {金融,银行,保险,投资,理财,贷款,支付,证券,基金,信贷}
- 人工智能 (ID:2002) - 关键词: {AI,人工智能,机器学习,深度学习,算法,智能,自动化,机器人,神经网络}
- 教育培训 (ID:2003) - 关键词: {教育,培训,学习,课程,在线教育,知识付费,技能培训,学校,老师}
- 医疗健康 (ID:2004) - 关键词: {医疗,健康,医院,医生,药品,诊断,治疗,体检,养生,保健}
- 物流运输 (ID:2005) - 关键词: {物流,运输,快递,配送,仓储,供应链,货运,邮寄,发货}
- 房地产 (ID:2006) - 关键词: {房地产,房产,买房,租房,装修,建筑,地产,楼盘,中介}
- 旅游出行 (ID:2007) - 关键词: {旅游,出行,酒店,机票,景点,度假,民宿,自驾,攻略}
- 餐饮美食 (ID:2008) - 关键词: {餐饮,美食,外卖,餐厅,菜谱,烹饪,食材,饮品,小吃}
- 娱乐传媒 (ID:2009) - 关键词: {娱乐,传媒,游戏,视频,音乐,直播,社交,内容,媒体}
- 企业服务 (ID:2010) - 关键词: {企业服务,SaaS,办公,管理,协作,云服务,CRM,ERP,工具}
- 制造业 (ID:2011) - 关键词: {制造,生产,工厂,加工,制造业,工业,设备,自动化,质量控制}
- 农业 (ID:2012) - 关键词: {农业,种植,养殖,农产品,农场,农民,农村,绿色,有机}
- 能源环保 (ID:2013) - 关键词: {能源,环保,新能源,节能,绿色,清洁,可再生,环境,碳中和}
- 政府公共 (ID:2014) - 关键词: {政府,公共服务,政务,民生,社会,公益,便民,数字政府}

### 💼 职级信息参考 (基于 job_listings 表实际数据)

#### 常见职级格式

- **阿里系**: P6, P7, P8, P9 (如: P6,P7 | P7,P8 | P7,P8,P9)
- **腾讯系**: 无 (大部分职位未标注具体职级)
- **滴滴系**: D8, D9 (如: D8 | D8,D9)
- **美团系**: L8, L9, L10 (如: L8,L9,L10)
- **小红书系**: R5, R6 (如: R5,R6 | R6)
- **其他**: E5-E7, 无职级标注

#### 薪资范围参考 (万元/年)

- **30-80 万**: 初级算法工程师
- **50-100 万**: 中级算法工程师
- **50-150 万**: 高级算法工程师
- **100-150 万**: 算法专家
- **100-250 万**: 资深算法专家
- **100-400 万**: 大模型/多模态专家
- **150-300 万**: 顶级算法专家

### 📋 职位样例 (job_listings 表实际数据)

#### 推荐算法类

- 搜索算法工程师 (ID:366) - 识货 | 推荐算法 | 电商零售 | 50-100 万
- 瓴羊-大模型高级算法工程师/专家 (ID:361) - 阿里巴巴 | 通用 ML | 人工智能 | P6,P7 | 100-150 万

#### 广告算法类

- 搜索广告算法工程师-商业算法 (ID:374) - 小红书 | 广告算法 | 人工智能 | R6 | 100-150 万
- 广告算法工程师/专家（国际化业务） (ID:365) - Bigo | 广告算法 | 娱乐传媒 | 100-250 万
- 展示广告算法工程师-商业算法 (ID:400) - 小红书 | 广告算法 | 人工智能 | R5,R6 | 50-150 万

#### 大模型算法类

- 大模型算法专家 (ID:385) - 阿里巴巴 | NLP 算法 | 自动驾驶 | 100-150 万
- 基座大模型算法专家 (ID:382) - 美团 | 大模型算法 | 人工智能 | L8,L9,L10 | 100-400 万
- AI 大模型算法资深专家 (ID:393) - 中兴通讯 | 大模型算法 | 人工智能 | 50-150 万
- 大模型算法工程师 (ID:402) - 百融云创 | 大模型算法 | 自动驾驶 | 50-150 万
- LLab-大模型算法工程师/专家(语言+文字) (ID:375) - 滴滴 | 大模型算法 | 人工智能 | D8,D9 | 100-250 万

#### 计算机视觉类

- 生成式 AI/世界模型算法工程师/专家-视觉团队 P7 (ID:370) - 高德地图 | CV 算法 | 自动驾驶 | P7 | 100-150 万

#### 多模态算法类

- (算法平台)多模态算法专家 (ID:381) - 得物 | 多模态算法 | 人工智能 | 100-150 万
- 集团安全部-大模型多模态安全算法专家-杭州 (ID:383) - 阿里巴巴 | 多模态算法 | 人工智能 | P7 | 100-150 万
- 大模型应用算法专家 (ID:377) - 小红书 | 多模态算法 | 人工智能 | R6 | 100-150 万
- 智能信息-夸克-VL 智能体算法专家-夸克 AI 相机 (ID:390) - 阿里巴巴 | 多模态算法 | 人工智能 | P7,P8,P9 | 50-250 万
- 智能信息-大模型高级工程师/算法专家-全模态方向 (ID:356) - 阿里巴巴 | 多模态算法 | 自动驾驶 | P7,P8 | 150-300 万

#### 自动驾驶算法类

- 多模态大模型/端到端自动驾驶算法工程师/专家 P7 (ID:371) - 高德地图 | 自动驾驶算法 | 人工智能 | P7 | 100-150 万
- 规控算法专家/高级算法工程师-视觉团队 P7 (ID:369) - 高德地图 | 自动驾驶算法 | 自动驾驶 | P7 | 100-150 万
- 感知算法工程师 (ID:392) - 徐工集团 | 自动驾驶算法 | 自动驾驶 | 30-80 万
- 规控算法工程师 E5-E7 (ID:388) - 哈啰出行 | 自动驾驶算法 | 人工智能 | 50-150 万

#### 其他算法类

- SandwichLab-RAG 算法工程师 (ID:362) - 思睿明智 | 通用 ML | 人工智能 | 50-150 万
- SandwichLab-大模型应用算法专家（Agent 方向） (ID:364) - 思睿明智 | 通用 ML | 人工智能 | 50-150 万
- 高级/中级运动控制算法工程师 (ID:403) - 阿里巴巴 | 通用 ML | 自动驾驶 | 50-150 万
- AI agent 算法专家 (ID:405) - 荣耀 | 通用 ML | 人工智能 | 100-150 万
- 算法专家 D8 (ID:358) - 滴滴 | 大模型算法 | 人工智能 | D8 | 100-150 万
- NLP/搜推/大模型算法工程师/专家(P6/7）-POI 智能化（急招） (ID:380) - 高德地图 | NLP 算法 | 旅游出行 | P6,P7 | 50-150 万
- （算法平台）算法工程师/专家（智能客服） (ID:397) - 得物 | 智能客服算法 | 人工智能 | 50-150 万
- 算法专家（用户增长） (ID:394) - SHEIN | 电商算法 | 电商零售 | 50-200 万
- 智能信息-夸克-搜索算法专家-排序/NLP 方向 (ID:376) - 阿里巴巴 | 搜索算法 | 人工智能 | P7,P8 | 100-250 万
- 运筹优化算法工程师 (ID:387) - 重庆赛迪奇智人工智能科技 | 智能物流算法 | 政府公共 | 50-150 万

---

## ⚠️ 重要提醒

1. **严格按照数据库映射**: 所有 mapping-tables.js 中的映射必须对应数据库中实际存在的 ID 和名称
2. **不允许虚构数据**: 不能创造数据库中不存在的公司、技术方向或业务场景
3. **职级信息来源**: 职级信息直接来自 job_listings 表的 job_level_raw 字段，无独立职级对照表
4. **Katrina 定位**: 专注算法猎头，只服务算法相关技术方向的候选人
5. **数据更新**: 本文档基于 2025 年 7 月 31 日的数据库状态，如有变化需及时更新
