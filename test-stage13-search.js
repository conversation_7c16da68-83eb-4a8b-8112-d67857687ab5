/**
 * 阶段13测试：职位搜索条件构建
 */

const DatabaseManager = require('./core/数据管理/database-manager.js');

console.log('🎯 阶段13测试：职位搜索条件构建\n');

async function testStage13() {
  try {
    // 模拟数据库管理器
    const mockSupabase = {
      from: (table) => ({
        select: (fields) => ({
          eq: (field, value) => {
            console.log(`✅ 精确匹配: ${field} = ${value}`);
            return mockSupabase.from(table).select(fields);
          },
          gte: (field, value) => {
            console.log(`✅ 大于等于: ${field} >= ${value}`);
            return mockSupabase.from(table).select(fields);
          },
          lte: (field, value) => {
            console.log(`✅ 小于等于: ${field} <= ${value}`);
            return mockSupabase.from(table).select(fields);
          },
          ilike: (field, value) => {
            console.log(`✅ 模糊匹配: ${field} ILIKE ${value}`);
            return mockSupabase.from(table).select(fields);
          },
          order: (field, options) => {
            console.log(`✅ 排序: ${field} ${options.ascending ? 'ASC' : 'DESC'}`);
            return mockSupabase.from(table).select(fields);
          },
          limit: (count) => {
            console.log(`✅ 限制: LIMIT ${count}`);
            return { data: [], error: null };
          }
        })
      })
    };

    const databaseManager = new DatabaseManager(mockSupabase);

    // 测试用例1：技术方向ID精确匹配
    console.log('📋 测试用例1：技术方向ID精确匹配');
    const criteria1 = {
      primary_tech_direction_id: 2,
      limit: 10
    };
    await databaseManager.searchJobs(criteria1);
    console.log('');

    // 测试用例2：业务场景ID精确匹配
    console.log('📋 测试用例2：业务场景ID精确匹配');
    const criteria2 = {
      primary_business_scenario_id: 5,
      limit: 10
    };
    await databaseManager.searchJobs(criteria2);
    console.log('');

    // 测试用例3：薪资范围区间查询
    console.log('📋 测试用例3：薪资范围区间查询');
    const criteria3 = {
      salary_range: { min: 20000, max: 50000 },
      limit: 10
    };
    await databaseManager.searchJobs(criteria3);
    console.log('');

    // 测试用例4：职级范围查询
    console.log('📋 测试用例4：职级范围查询');
    const criteria4 = {
      level_range: { min_level: 3, max_level: 6 },
      limit: 10
    };
    await databaseManager.searchJobs(criteria4);
    console.log('');

    // 测试用例5：公司类型过滤
    console.log('📋 测试用例5：公司类型过滤');
    const criteria5 = {
      company_type: "bigtech",
      limit: 10
    };
    await databaseManager.searchJobs(criteria5);
    console.log('');

    // 测试用例6：综合查询
    console.log('📋 测试用例6：综合查询');
    const criteria6 = {
      primary_tech_direction_id: 2,
      primary_business_scenario_id: 5,
      salary_range: { min: 20000, max: 50000 },
      level_range: { min_level: 3, max_level: 6 },
      company_type: "bigtech",
      limit: 20
    };
    await databaseManager.searchJobs(criteria6);

    console.log('\n🎯 阶段13测试完成');
    console.log('📊 测试总结:');
    console.log('- 技术方向ID精确匹配: ✅ 成功');
    console.log('- 业务场景ID精确匹配: ✅ 成功');
    console.log('- 薪资范围区间查询: ✅ 成功');
    console.log('- 职级范围查询: ✅ 成功');
    console.log('- 公司类型过滤: ✅ 成功');
    console.log('- 综合查询: ✅ 成功');

  } catch (error) {
    console.error('❌ 阶段13测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

testStage13().catch(console.error);
