{"timestamp": "2025-07-31T08:25:37.353Z", "summary": {"totalTests": 18, "successTests": 12, "failureTests": 6, "partialTests": 0}, "results": [{"test": "Node.js环境检查", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.142Z", "nodeVersion": "v20.19.2", "platform": "darwin", "cwd": "/Users/<USER>/Desktop/ai-recruitment-assistant-fixed"}, {"test": "依赖模块: axios", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.143Z", "error": "Cannot find module 'axios'\nRequire stack:\n- /Users/<USER>/Desktop/ai-recruitment-assistant-fixed/core-test.js"}, {"test": "依赖模块: uuid", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.143Z", "error": "Cannot find module 'uuid'\nRequire stack:\n- /Users/<USER>/Desktop/ai-recruitment-assistant-fixed/core-test.js"}, {"test": "依赖模块: dotenv", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.144Z", "error": "Cannot find module 'dotenv'\nRequire stack:\n- /Users/<USER>/Desktop/ai-recruitment-assistant-fixed/core-test.js"}, {"test": "环境变量加载", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.144Z", "error": "Cannot find module 'dotenv'\nRequire stack:\n- /Users/<USER>/Desktop/ai-recruitment-assistant-fixed/core-test.js"}, {"test": "文件存在: app-config.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.144Z"}, {"test": "文件存在: message-processor.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.144Z"}, {"test": "文件存在: database-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.144Z"}, {"test": "文件存在: ai-services.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.144Z"}, {"test": "文件存在: user-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.145Z"}, {"test": "语法检查: app-config.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.201Z"}, {"test": "语法检查: message-processor.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.289Z"}, {"test": "语法检查: database-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.342Z"}, {"test": "语法检查: ai-services.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.342Z"}, {"test": "语法检查: user-manager.js", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.343Z"}, {"test": "AppConfig实例化", "status": "SUCCESS", "timestamp": "2025-07-31T08:25:37.343Z"}, {"test": "基础功能测试", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.351Z", "error": "配置验证失败: \"database.supabaseUrl\" is required"}, {"test": "网络连接测试", "status": "FAILURE", "timestamp": "2025-07-31T08:25:37.352Z", "error": "Cannot find module 'axios'\nRequire stack:\n- /Users/<USER>/Desktop/ai-recruitment-assistant-fixed/core-test.js"}]}