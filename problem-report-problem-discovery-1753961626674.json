{"testId": "problem-discovery-1753961626674", "startTime": "2025-07-31T11:33:46.674Z", "endTime": "2025-07-31T11:36:23.328Z", "totalTime": 156654, "problemsSummary": {"total": 11, "critical": 0, "high": 6, "medium": 5, "low": 0}, "problems": [{"category": "信息提取缺陷", "testCase": "基础信息提取", "input": "我是Java开发工程师，有3年经验", "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解技术栈: Java", "未能提取或理解工作经验: 3年", "未能提取或理解职位: 开发工程师"], "systemResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "HIGH"}, {"category": "信息提取缺陷", "testCase": "复杂信息提取", "input": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解技术栈: Python", "未能提取或理解职位: 算法工程师", "未能提取或理解工作经验: 5年", "未能提取或理解期望薪资: 40k", "未能提取或理解工作地点: 北京"], "systemResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "HIGH"}, {"category": "信息提取缺陷", "testCase": "公司背景信息", "input": "我在阿里巴巴工作过2年，现在想转到字节跳动", "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解当前公司: 阿里巴巴", "未能提取或理解工作经验: 2年", "未能提取或理解目标公司: 字节跳动"], "systemResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "HIGH"}, {"category": "信息提取缺陷", "testCase": "技术栈组合", "input": "我熟悉React、Node.js、MySQL，做过微服务架构", "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解前端技术: React", "未能提取或理解后端技术: Node.js", "未能提取或理解数据库: MySQL", "未能提取或理解架构经验: 微服务"], "systemResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "HIGH"}, {"category": "系统理解能力问题", "testCase": "明确求职意图", "input": "我想找一份工作", "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "MEDIUM"}, {"category": "意图识别错误", "testCase": "职位推荐请求", "input": "有什么职位推荐吗", "expected": "JOB_SEARCH", "actual": "job_search", "severity": "HIGH"}, {"category": "意图识别错误", "testCase": "技术咨询", "input": "我是Python开发，想了解市场行情", "expected": "JOB_SEARCH", "actual": "SALARY_INQUIRY", "severity": "HIGH"}, {"category": "系统理解能力问题", "testCase": "技术咨询", "input": "我是Python开发，想了解市场行情", "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "MEDIUM"}, {"category": "信息收集触发失败", "testCase": "技术背景提及", "input": "我是Java工程师", "expectedTrigger": "技术栈触发", "actualResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "MEDIUM"}, {"category": "信息收集触发失败", "testCase": "薪资期望表达", "input": "期望薪资30k左右", "expectedTrigger": "薪资触发", "actualResponse": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "severity": "MEDIUM"}, {"category": "响应质量问题", "testCase": "响应质量评估", "input": "我想找一份前端开发的工作", "response": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "issues": ["对明确的求职需求返回了\"不理解\"的通用回复", "未能体现对用户技术方向的理解"], "severity": "MEDIUM"}], "testResults": [{"category": "INFORMATION_EXTRACTION", "testCase": "基础信息提取", "input": {"message": "我是Java开发工程师，有3年经验", "userEmail": "<EMAIL>", "sessionId": "bc658c49-5229-4b84-ba4b-d3d9ad8c480e"}, "response": {"success": true, "sessionId": "bc658c49-5229-4b84-ba4b-d3d9ad8c480e", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:33:58.235Z"}, "responseTime": 11570, "extractionResults": {"extractedInfo": {}, "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解技术栈: Java", "未能提取或理解工作经验: 3年", "未能提取或理解职位: 开发工程师"], "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项："}, "status": "FAIL"}, {"category": "INFORMATION_EXTRACTION", "testCase": "复杂信息提取", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "578f6c8f-c8fb-4598-b40d-0dd532fdf6a0"}, "response": {"success": true, "sessionId": "578f6c8f-c8fb-4598-b40d-0dd532fdf6a0", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:34:13.801Z"}, "responseTime": 14556, "extractionResults": {"extractedInfo": {}, "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解技术栈: Python", "未能提取或理解职位: 算法工程师", "未能提取或理解工作经验: 5年", "未能提取或理解期望薪资: 40k", "未能提取或理解工作地点: 北京"], "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项："}, "status": "FAIL"}, {"category": "INFORMATION_EXTRACTION", "testCase": "公司背景信息", "input": {"message": "我在阿里巴巴工作过2年，现在想转到字节跳动", "userEmail": "<EMAIL>", "sessionId": "59a3e86c-79da-4827-a572-024b037ba92e"}, "response": {"success": true, "sessionId": "59a3e86c-79da-4827-a572-024b037ba92e", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:34:25.171Z"}, "responseTime": 10366, "extractionResults": {"extractedInfo": {}, "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解当前公司: 阿里巴巴", "未能提取或理解工作经验: 2年", "未能提取或理解目标公司: 字节跳动"], "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项："}, "status": "FAIL"}, {"category": "INFORMATION_EXTRACTION", "testCase": "技术栈组合", "input": {"message": "我熟悉React、Node.js、MySQL，做过微服务架构", "userEmail": "<EMAIL>", "sessionId": "69cf8d64-4a7a-4d5a-a563-110e61e51777"}, "response": {"success": true, "sessionId": "69cf8d64-4a7a-4d5a-a563-110e61e51777", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:34:38.606Z"}, "responseTime": 12434, "extractionResults": {"extractedInfo": {}, "problems": ["系统无法理解明确的求职信息，返回了\"不理解\"的响应", "未能提取或理解前端技术: React", "未能提取或理解后端技术: Node.js", "未能提取或理解数据库: MySQL", "未能提取或理解架构经验: 微服务"], "responseType": "unknown_intent", "responseContent": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项："}, "status": "FAIL"}]}