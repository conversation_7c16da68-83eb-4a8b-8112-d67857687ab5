/**
 * 不可造假的深度系统集成测试
 *
 * 测试目标：
 * 1. 双模型调用真实性验证
 * 2. 信息收集触发机制完整性测试
 * 3. 推荐系统完整链路测试
 * 4. 对话终止逻辑验证
 * 5. 容错机制异常注入测试
 * 6. 并发100x冷启动压力测试
 */

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");

class ComprehensiveSystemTester {
  constructor() {
    this.baseUrl = "http://localhost:6789";
    this.testResults = [];
    this.startTime = Date.now();
    this.concurrentRequests = [];

    // 测试配置
    this.config = {
      concurrentTestCount: 100,
      timeoutMs: 30000,
      retryAttempts: 0, // 禁止重试确保真实性
      coldStartDelay: 100, // 冷启动间隔
    };
  }

  /**
   * 执行完整测试套件
   */
  async runComprehensiveTests() {
    console.log("🚀 开始执行不可造假的深度系统集成测试");
    console.log("=".repeat(80));

    try {
      // 1. 系统健康检查
      await this.testSystemHealth();

      // 2. 双模型调用真实性测试
      await this.testDualModelCalls();

      // 3. 信息收集触发机制测试
      await this.testInfoCollectionTriggers();

      // 4. 推荐系统完整链路测试
      await this.testRecommendationPipeline();

      // 5. 对话终止逻辑测试
      await this.testConversationTermination();

      // 6. 容错机制异常注入测试
      await this.testErrorHandlingMechanisms();

      // 7. 并发100x冷启动测试
      await this.testConcurrentColdStart();

      // 8. 生成详细测试报告
      this.generateDetailedReport();
    } catch (error) {
      console.error("❌ 测试执行失败:", error);
      this.logTestResult("SYSTEM_FAILURE", {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 测试1: 系统健康检查
   */
  async testSystemHealth() {
    console.log("\n📊 测试1: 系统健康检查");
    console.log("-".repeat(50));

    const testCase = {
      name: "系统健康检查",
      startTime: Date.now(),
    };

    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: this.config.timeoutMs,
      });

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.response = response.data;
      testCase.httpStatus = response.status;

      console.log("✅ 系统健康检查通过");
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 响应内容:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log("❌ 系统健康检查失败");
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
    }

    this.logTestResult("HEALTH_CHECK", testCase);
  }

  /**
   * 测试2: 双模型调用真实性验证
   */
  async testDualModelCalls() {
    console.log("\n🤖 测试2: 双模型调用真实性验证");
    console.log("-".repeat(50));

    const testCases = [
      {
        name: "DeepSeek模型调用测试",
        input: { message: "你好，我想找工作", userEmail: "<EMAIL>" },
        expectedModel: "deepseek",
      },
      {
        name: "Qwen模型调用测试",
        input: {
          message: "我是Java开发工程师，有5年经验",
          userEmail: "<EMAIL>",
        },
        expectedModel: "qwen",
      },
      {
        name: "模型切换测试",
        input: { message: "推荐一些职位给我", userEmail: "<EMAIL>" },
        expectedModel: "both",
      },
    ];

    for (const testCase of testCases) {
      await this.executeDualModelTest(testCase);
      await this.delay(this.config.coldStartDelay); // 冷启动间隔
    }
  }

  /**
   * 执行双模型测试
   */
  async executeDualModelTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        {
          ...testCase.input,
          sessionId: sessionId,
        },
        {
          timeout: this.config.timeoutMs,
          headers: { "Content-Type": "application/json" },
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 验证响应结构
      this.validateResponseStructure(testCase, response.data);

      console.log(`✅ 测试通过: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 原始响应:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;
      testCase.httpStatus = error.response?.status || "TIMEOUT";

      console.log(`❌ 测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      if (error.response) {
        console.log(
          `📋 错误响应:`,
          JSON.stringify(error.response.data, null, 2)
        );
      }
    }

    this.logTestResult("DUAL_MODEL_CALL", testCase);
  }

  /**
   * 测试3: 信息收集触发机制测试
   */
  async testInfoCollectionTriggers() {
    console.log("\n📋 测试3: 信息收集触发机制测试");
    console.log("-".repeat(50));

    const triggerTests = [
      {
        name: "主动触发 - 职位询问",
        input: "有什么职位推荐吗",
        expectedTrigger: true,
        triggerType: "active",
      },
      {
        name: "关键词触发 - 技术栈提及",
        input: "我是Python开发工程师",
        expectedTrigger: true,
        triggerType: "keyword",
      },
      {
        name: "上下文触发 - 信息补充",
        input: "我在阿里巴巴工作",
        expectedTrigger: true,
        triggerType: "context",
      },
      {
        name: "非预期输入 - 无关内容",
        input: "今天天气真好",
        expectedTrigger: false,
        triggerType: "none",
      },
      {
        name: "边界测试 - 空字符串",
        input: "",
        expectedTrigger: false,
        triggerType: "none",
      },
      {
        name: "边界测试 - 特殊字符",
        input: "!@#$%^&*()",
        expectedTrigger: false,
        triggerType: "none",
      },
    ];

    for (const test of triggerTests) {
      await this.executeInfoCollectionTest(test);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行信息收集触发测试
   */
  async executeInfoCollectionTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行触发测试: ${testCase.name}`);
      console.log(`📝 输入内容: "${testCase.input}"`);
      console.log(`🎯 预期触发: ${testCase.expectedTrigger}`);

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        {
          message: testCase.input,
          userEmail: `trigger-test-${Date.now()}@example.com`,
          sessionId: sessionId,
        },
        {
          timeout: this.config.timeoutMs,
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.rawResponse = response.data;

      // 分析是否触发了信息收集
      const actualTrigger = this.analyzeInfoCollectionTrigger(response.data);
      testCase.actualTrigger = actualTrigger;
      testCase.triggerMatch = actualTrigger === testCase.expectedTrigger;

      if (testCase.triggerMatch) {
        console.log(
          `✅ 触发机制正确: 预期=${testCase.expectedTrigger}, 实际=${actualTrigger}`
        );
      } else {
        console.log(
          `❌ 触发机制错误: 预期=${testCase.expectedTrigger}, 实际=${actualTrigger}`
        );
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 原始响应:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 触发测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("INFO_COLLECTION_TRIGGER", testCase);
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 验证响应结构
   */
  validateResponseStructure(testCase, responseData) {
    const requiredFields = ["success", "response", "timestamp"];
    const missingFields = requiredFields.filter(
      (field) => !(field in responseData)
    );

    if (missingFields.length > 0) {
      testCase.validationErrors = [`缺少必需字段: ${missingFields.join(", ")}`];
    } else {
      testCase.validationErrors = [];
    }

    testCase.responseStructureValid = missingFields.length === 0;
  }

  /**
   * 分析信息收集触发情况
   */
  analyzeInfoCollectionTrigger(responseData) {
    if (!responseData.success || !responseData.response) {
      return false;
    }

    const content = responseData.response.content || "";
    const triggerKeywords = [
      "请告诉我",
      "能否告诉我",
      "您的技术",
      "工作经验",
      "期望薪资",
      "技术栈",
      "技术方向",
      "所在公司",
    ];

    return triggerKeywords.some((keyword) => content.includes(keyword));
  }

  /**
   * 测试4: 推荐系统完整链路测试
   */
  async testRecommendationPipeline() {
    console.log("\n🎯 测试4: 推荐系统完整链路测试");
    console.log("-".repeat(50));

    const pipelineTests = [
      {
        name: "技术映射测试 - 有效技术栈",
        input: {
          message: "我是Java后端开发，有3年经验",
          userEmail: "<EMAIL>",
        },
        expectedMapping: true,
      },
      {
        name: "技术映射测试 - 无效技术栈",
        input: { message: "我是厨师，会做菜", userEmail: "<EMAIL>" },
        expectedMapping: false,
      },
      {
        name: "业务映射测试 - 电商场景",
        input: {
          message: "我想在电商公司工作，做推荐算法",
          userEmail: "<EMAIL>",
        },
        expectedMapping: true,
      },
      {
        name: "推荐矩阵测试 - 完整信息",
        input: {
          message: "我是Python算法工程师，在阿里工作，P6级别，期望40k",
          userEmail: "<EMAIL>",
        },
        expectedRecommendation: true,
      },
      {
        name: "推荐矩阵测试 - 信息不足",
        input: { message: "我想找工作", userEmail: "<EMAIL>" },
        expectedRecommendation: false,
      },
    ];

    for (const test of pipelineTests) {
      await this.executePipelineTest(test);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行推荐系统管道测试
   */
  async executePipelineTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行管道测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        {
          ...testCase.input,
          sessionId: sessionId,
        },
        {
          timeout: this.config.timeoutMs,
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.rawResponse = response.data;

      // 分析推荐系统响应
      testCase.mappingResult = this.analyzeMappingResult(response.data);
      testCase.recommendationResult = this.analyzeRecommendationResult(
        response.data
      );

      console.log(`✅ 管道测试完成: ${testCase.name}`);
      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`🗺️ 映射结果: ${testCase.mappingResult}`);
      console.log(`🎯 推荐结果: ${testCase.recommendationResult}`);
      console.log(`📋 原始响应:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 管道测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("RECOMMENDATION_PIPELINE", testCase);
  }

  /**
   * 测试5: 对话终止逻辑测试
   */
  async testConversationTermination() {
    console.log("\n🔚 测试5: 对话终止逻辑测试");
    console.log("-".repeat(50));

    const terminationTests = [
      {
        name: "用户主动终止",
        messages: ["你好", "我想找工作", "算了，不找了", "再见"],
        expectedTermination: true,
      },
      {
        name: "系统超时终止",
        messages: ["你好"],
        waitTime: 5000, // 等待5秒测试超时
        expectedTermination: true,
      },
      {
        name: "无限循环检测",
        messages: ["推荐职位", "推荐职位", "推荐职位", "推荐职位", "推荐职位"],
        expectedTermination: true,
      },
      {
        name: "正常对话流程",
        messages: ["你好", "我是Java开发", "有3年经验", "期望30k"],
        expectedTermination: false,
      },
    ];

    for (const test of terminationTests) {
      await this.executeTerminationTest(test);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行对话终止测试
   */
  async executeTerminationTest(testCase) {
    const sessionId = uuidv4();
    testCase.sessionId = sessionId;
    testCase.startTime = Date.now();
    testCase.messageResponses = [];

    try {
      console.log(`\n🔍 执行终止测试: ${testCase.name}`);
      console.log(`📝 消息序列:`, testCase.messages);

      for (let i = 0; i < testCase.messages.length; i++) {
        const message = testCase.messages[i];
        const messageStart = Date.now();

        try {
          const response = await axios.post(
            `${this.baseUrl}/api/chat`,
            {
              message: message,
              userEmail: `termination-test-${Date.now()}@example.com`,
              sessionId: sessionId,
            },
            {
              timeout: this.config.timeoutMs,
            }
          );

          const messageResponse = {
            messageIndex: i,
            message: message,
            responseTime: Date.now() - messageStart,
            success: true,
            response: response.data,
          };

          testCase.messageResponses.push(messageResponse);

          // 检查是否触发了终止逻辑
          if (this.detectTermination(response.data)) {
            testCase.terminationTriggered = true;
            testCase.terminationAt = i;
            break;
          }
        } catch (error) {
          const messageResponse = {
            messageIndex: i,
            message: message,
            responseTime: Date.now() - messageStart,
            success: false,
            error: error.message,
          };

          testCase.messageResponses.push(messageResponse);
        }

        await this.delay(100); // 消息间隔
      }

      // 如果有等待时间，执行等待测试
      if (testCase.waitTime) {
        console.log(`⏰ 等待 ${testCase.waitTime}ms 测试超时终止...`);
        await this.delay(testCase.waitTime);
      }

      testCase.endTime = Date.now();
      testCase.totalTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";

      console.log(`✅ 终止测试完成: ${testCase.name}`);
      console.log(`📊 总耗时: ${testCase.totalTime}ms`);
      console.log(`🔚 终止触发: ${testCase.terminationTriggered || false}`);
      console.log(`📋 消息响应数: ${testCase.messageResponses.length}`);
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.totalTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 终止测试失败: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("CONVERSATION_TERMINATION", testCase);
  }

  /**
   * 测试6: 容错机制异常注入测试
   */
  async testErrorHandlingMechanisms() {
    console.log("\n🛡️ 测试6: 容错机制异常注入测试");
    console.log("-".repeat(50));

    const errorTests = [
      {
        name: "空消息注入",
        input: { message: null, userEmail: "<EMAIL>" },
        expectedError: true,
      },
      {
        name: "超长消息注入",
        input: { message: "A".repeat(10000), userEmail: "<EMAIL>" },
        expectedError: true,
      },
      {
        name: "无效邮箱格式",
        input: { message: "你好", userEmail: "invalid-email" },
        expectedError: true,
      },
      {
        name: "特殊字符注入",
        input: {
          message: '<script>alert("xss")</script>',
          userEmail: "<EMAIL>",
        },
        expectedError: false, // 应该被正常处理
      },
      {
        name: "SQL注入尝试",
        input: {
          message: "'; DROP TABLE users; --",
          userEmail: "<EMAIL>",
        },
        expectedError: false, // 应该被正常处理
      },
      {
        name: "无效sessionId",
        input: {
          message: "你好",
          userEmail: "<EMAIL>",
          sessionId: "invalid-uuid",
        },
        expectedError: false, // 应该创建新会话
      },
    ];

    for (const test of errorTests) {
      await this.executeErrorTest(test);
      await this.delay(this.config.coldStartDelay);
    }
  }

  /**
   * 执行容错测试
   */
  async executeErrorTest(testCase) {
    testCase.startTime = Date.now();

    try {
      console.log(`\n🔍 执行容错测试: ${testCase.name}`);
      console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        testCase.input,
        {
          timeout: this.config.timeoutMs,
          validateStatus: () => true, // 接受所有状态码
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      // 分析错误处理结果
      const isError =
        response.status >= 400 || (response.data && !response.data.success);
      testCase.actualError = isError;
      testCase.errorHandlingCorrect = isError === testCase.expectedError;

      if (testCase.errorHandlingCorrect) {
        testCase.status = "SUCCESS";
        console.log(`✅ 容错测试通过: ${testCase.name}`);
        console.log(
          `🎯 错误处理正确: 预期=${testCase.expectedError}, 实际=${isError}`
        );
      } else {
        testCase.status = "FAILURE";
        console.log(`❌ 容错测试失败: ${testCase.name}`);
        console.log(
          `🎯 错误处理错误: 预期=${testCase.expectedError}, 实际=${isError}`
        );
      }

      console.log(`📊 响应时间: ${testCase.responseTime}ms`);
      console.log(`📋 HTTP状态: ${response.status}`);
      console.log(`📋 原始响应:`, JSON.stringify(response.data, null, 2));
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "NETWORK_ERROR";
      testCase.error = error.message;
      testCase.errorStack = error.stack;

      console.log(`❌ 容错测试网络错误: ${testCase.name}`);
      console.log(`💥 错误信息: ${error.message}`);
    }

    this.logTestResult("ERROR_HANDLING", testCase);
  }

  /**
   * 测试7: 并发100x冷启动压力测试
   */
  async testConcurrentColdStart() {
    console.log("\n🚀 测试7: 并发100x冷启动压力测试");
    console.log("-".repeat(50));

    const concurrentTests = [];
    const testMessages = [
      "你好，我想找工作",
      "我是Java开发工程师",
      "有什么职位推荐吗",
      "我在阿里巴巴工作",
      "期望薪资30k",
    ];

    console.log(`🎯 准备发起 ${this.config.concurrentTestCount} 个并发请求...`);

    // 创建并发测试任务
    for (let i = 0; i < this.config.concurrentTestCount; i++) {
      const testCase = {
        name: `并发测试-${i + 1}`,
        sessionId: uuidv4(),
        message: testMessages[i % testMessages.length],
        userEmail: `concurrent-${i}@test.com`,
        testIndex: i,
      };

      concurrentTests.push(this.executeConcurrentTest(testCase));
    }

    const startTime = Date.now();
    console.log(`⏰ 开始时间: ${new Date(startTime).toISOString()}`);

    // 执行所有并发测试
    const results = await Promise.allSettled(concurrentTests);

    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // 分析并发测试结果
    const successCount = results.filter(
      (r) => r.status === "fulfilled" && r.value.status === "SUCCESS"
    ).length;
    const failureCount = results.filter(
      (r) =>
        r.status === "rejected" ||
        (r.status === "fulfilled" && r.value.status !== "SUCCESS")
    ).length;
    const successRate = (
      (successCount / this.config.concurrentTestCount) *
      100
    ).toFixed(2);

    console.log("\n📊 并发测试结果统计:");
    console.log(`⏰ 总耗时: ${totalTime}ms`);
    console.log(
      `✅ 成功请求: ${successCount}/${this.config.concurrentTestCount}`
    );
    console.log(
      `❌ 失败请求: ${failureCount}/${this.config.concurrentTestCount}`
    );
    console.log(`📈 成功率: ${successRate}%`);
    console.log(
      `⚡ 平均响应时间: ${(totalTime / this.config.concurrentTestCount).toFixed(
        2
      )}ms`
    );

    // 记录详细的并发测试结果
    const concurrentTestResult = {
      name: "并发100x冷启动测试",
      startTime: startTime,
      endTime: endTime,
      totalTime: totalTime,
      concurrentCount: this.config.concurrentTestCount,
      successCount: successCount,
      failureCount: failureCount,
      successRate: parseFloat(successRate),
      averageResponseTime: totalTime / this.config.concurrentTestCount,
      individualResults: results.map((result, index) => ({
        testIndex: index,
        status: result.status,
        value: result.status === "fulfilled" ? result.value : null,
        reason: result.status === "rejected" ? result.reason : null,
      })),
    };

    this.logTestResult("CONCURRENT_COLD_START", concurrentTestResult);
  }

  /**
   * 执行单个并发测试
   */
  async executeConcurrentTest(testCase) {
    testCase.startTime = Date.now();

    try {
      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        {
          message: testCase.message,
          userEmail: testCase.userEmail,
          sessionId: testCase.sessionId,
        },
        {
          timeout: this.config.timeoutMs,
        }
      );

      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "SUCCESS";
      testCase.httpStatus = response.status;
      testCase.rawResponse = response.data;

      return testCase;
    } catch (error) {
      testCase.endTime = Date.now();
      testCase.responseTime = testCase.endTime - testCase.startTime;
      testCase.status = "FAILURE";
      testCase.error = error.message;
      testCase.httpStatus = error.response?.status || "TIMEOUT";

      return testCase;
    }
  }

  /**
   * 分析映射结果
   */
  analyzeMappingResult(responseData) {
    if (!responseData.success || !responseData.response) {
      return "NO_MAPPING";
    }

    const content = responseData.response.content || "";
    const mappingKeywords = ["技术栈", "技术方向", "映射", "匹配"];

    return mappingKeywords.some((keyword) => content.includes(keyword))
      ? "MAPPED"
      : "NOT_MAPPED";
  }

  /**
   * 分析推荐结果
   */
  analyzeRecommendationResult(responseData) {
    if (!responseData.success || !responseData.response) {
      return "NO_RECOMMENDATION";
    }

    const content = responseData.response.content || "";
    const recommendationKeywords = ["推荐", "职位", "岗位", "公司", "机会"];

    return recommendationKeywords.some((keyword) => content.includes(keyword))
      ? "RECOMMENDED"
      : "NOT_RECOMMENDED";
  }

  /**
   * 检测对话终止
   */
  detectTermination(responseData) {
    if (!responseData.success) {
      return true; // 系统错误视为终止
    }

    if (!responseData.response || !responseData.response.content) {
      return true; // 无响应内容视为终止
    }

    const content = responseData.response.content.toLowerCase();
    const terminationKeywords = ["再见", "结束", "终止", "不再", "停止"];

    return terminationKeywords.some((keyword) => content.includes(keyword));
  }

  /**
   * 生成详细测试报告
   */
  generateDetailedReport() {
    console.log("\n" + "=".repeat(80));
    console.log("📊 不可造假的深度系统集成测试 - 详细报告");
    console.log("=".repeat(80));

    const totalTime = Date.now() - this.startTime;
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(
      (t) => t.status === "SUCCESS"
    ).length;
    const failedTests = this.testResults.filter(
      (t) => t.status === "FAILURE" || t.status === "NETWORK_ERROR"
    ).length;
    const successRate =
      totalTests > 0 ? ((successfulTests / totalTests) * 100).toFixed(2) : 0;

    console.log(`\n📈 总体测试统计:`);
    console.log(
      `⏰ 总测试时间: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}秒)`
    );
    console.log(`🧪 总测试数量: ${totalTests}`);
    console.log(`✅ 成功测试: ${successfulTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📊 成功率: ${successRate}%`);

    // 按类别统计
    const categoryStats = {};
    this.testResults.forEach((test) => {
      if (!categoryStats[test.category]) {
        categoryStats[test.category] = { total: 0, success: 0, failure: 0 };
      }
      categoryStats[test.category].total++;
      if (test.status === "SUCCESS") {
        categoryStats[test.category].success++;
      } else {
        categoryStats[test.category].failure++;
      }
    });

    console.log(`\n📋 分类测试统计:`);
    Object.entries(categoryStats).forEach(([category, stats]) => {
      const categorySuccessRate = ((stats.success / stats.total) * 100).toFixed(
        2
      );
      console.log(
        `  ${category}: ${stats.success}/${stats.total} (${categorySuccessRate}%)`
      );
    });

    // 详细测试结果
    console.log(`\n📝 详细测试结果:`);
    this.testResults.forEach((test, index) => {
      console.log(
        `\n${index + 1}. ${test.category} - ${test.name || "未命名测试"}`
      );
      console.log(`   状态: ${test.status}`);
      console.log(`   时间: ${test.responseTime || test.totalTime || "N/A"}ms`);

      if (test.status === "SUCCESS") {
        console.log(`   ✅ 测试通过`);
      } else {
        console.log(`   ❌ 测试失败: ${test.error || "未知错误"}`);
      }

      // 显示关键测试数据
      if (test.rawResponse) {
        console.log(
          `   响应: ${JSON.stringify(test.rawResponse).substring(0, 100)}...`
        );
      }

      if (test.httpStatus) {
        console.log(`   HTTP状态: ${test.httpStatus}`);
      }
    });

    // 未测试区域报告
    console.log(`\n🚫 未测试区域:`);
    const untestedAreas = [
      "数据库连接池压力测试",
      "WebSocket实时通信测试",
      "文件上传功能测试",
      "第三方API依赖测试",
      "内存泄漏长期运行测试",
      "跨浏览器兼容性测试",
    ];

    untestedAreas.forEach((area) => {
      console.log(`   ⚠️ ${area} - 需要专门的测试环境和工具`);
    });

    // 性能分析
    const responseTimes = this.testResults
      .filter((t) => t.responseTime)
      .map((t) => t.responseTime);

    if (responseTimes.length > 0) {
      const avgResponseTime =
        responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);

      console.log(`\n⚡ 性能分析:`);
      console.log(`   平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   最大响应时间: ${maxResponseTime}ms`);
      console.log(`   最小响应时间: ${minResponseTime}ms`);
    }

    // 保存详细报告到文件
    this.saveReportToFile();

    console.log("\n" + "=".repeat(80));
    console.log("📊 测试报告生成完成");
    console.log("=".repeat(80));
  }

  /**
   * 保存报告到文件
   */
  saveReportToFile() {
    const fs = require("fs");
    const reportData = {
      testSummary: {
        startTime: new Date(this.startTime).toISOString(),
        endTime: new Date().toISOString(),
        totalTime: Date.now() - this.startTime,
        totalTests: this.testResults.length,
        successfulTests: this.testResults.filter((t) => t.status === "SUCCESS")
          .length,
        failedTests: this.testResults.filter((t) => t.status !== "SUCCESS")
          .length,
      },
      detailedResults: this.testResults,
      testConfig: this.config,
    };

    const fileName = `test-report-${new Date()
      .toISOString()
      .replace(/[:.]/g, "-")}.json`;

    try {
      fs.writeFileSync(fileName, JSON.stringify(reportData, null, 2));
      console.log(`📄 详细报告已保存到: ${fileName}`);
    } catch (error) {
      console.error(`❌ 保存报告失败: ${error.message}`);
    }
  }

  /**
   * 记录测试结果
   */
  logTestResult(category, testCase) {
    this.testResults.push({
      category,
      timestamp: new Date().toISOString(),
      ...testCase,
    });
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const tester = new ComprehensiveSystemTester();
  tester.runComprehensiveTests().catch(console.error);
}

module.exports = ComprehensiveSystemTester;
