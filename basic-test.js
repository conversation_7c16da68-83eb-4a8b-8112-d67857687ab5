/**
 * 基础测试 - 验证系统基本功能
 */

const axios = require('axios');

async function runBasicTest() {
  console.log('🚀 开始基础系统测试');
  console.log('=' .repeat(50));
  
  const baseUrl = 'http://localhost:6789';
  const results = [];
  
  // 测试1: 健康检查
  console.log('\n📊 测试1: 系统健康检查');
  try {
    const startTime = Date.now();
    const response = await axios.get(`${baseUrl}/health`, { timeout: 10000 });
    const responseTime = Date.now() - startTime;
    
    console.log(`✅ 健康检查通过`);
    console.log(`📊 响应时间: ${responseTime}ms`);
    console.log(`📋 响应内容:`, JSON.stringify(response.data, null, 2));
    
    results.push({
      test: '健康检查',
      status: 'SUCCESS',
      responseTime: responseTime,
      data: response.data
    });
    
  } catch (error) {
    console.log(`❌ 健康检查失败: ${error.message}`);
    results.push({
      test: '健康检查',
      status: 'FAILURE',
      error: error.message
    });
    
    // 如果健康检查失败，可能系统未启动
    console.log('⚠️ 系统可能未启动，请检查系统状态');
    return;
  }
  
  // 测试2: 初始化对话
  console.log('\n🔧 测试2: 初始化对话');
  try {
    const startTime = Date.now();
    const response = await axios.post(`${baseUrl}/api/chat`, {
      message: '__INIT__',
      userEmail: '<EMAIL>'
    }, { 
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' }
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`✅ 初始化对话成功`);
    console.log(`📊 响应时间: ${responseTime}ms`);
    console.log(`📋 响应摘要:`, {
      success: response.data.success,
      hasResponse: !!response.data.response,
      responseType: response.data.response?.type
    });
    
    results.push({
      test: '初始化对话',
      status: 'SUCCESS',
      responseTime: responseTime,
      data: response.data
    });
    
  } catch (error) {
    console.log(`❌ 初始化对话失败: ${error.message}`);
    console.log(`📋 错误详情:`, error.response?.data || 'No response data');
    
    results.push({
      test: '初始化对话',
      status: 'FAILURE',
      error: error.message,
      errorData: error.response?.data
    });
  }
  
  // 测试3: 简单对话
  console.log('\n💬 测试3: 简单对话');
  try {
    const startTime = Date.now();
    const response = await axios.post(`${baseUrl}/api/chat`, {
      message: '你好',
      userEmail: '<EMAIL>'
    }, { 
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' }
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`✅ 简单对话成功`);
    console.log(`📊 响应时间: ${responseTime}ms`);
    console.log(`📋 响应摘要:`, {
      success: response.data.success,
      hasResponse: !!response.data.response,
      responseType: response.data.response?.type,
      contentLength: response.data.response?.content?.length || 0
    });
    
    results.push({
      test: '简单对话',
      status: 'SUCCESS',
      responseTime: responseTime,
      data: response.data
    });
    
  } catch (error) {
    console.log(`❌ 简单对话失败: ${error.message}`);
    console.log(`📋 错误详情:`, error.response?.data || 'No response data');
    
    results.push({
      test: '简单对话',
      status: 'FAILURE',
      error: error.message,
      errorData: error.response?.data
    });
  }
  
  // 测试4: 职位询问
  console.log('\n🎯 测试4: 职位询问');
  try {
    const startTime = Date.now();
    const response = await axios.post(`${baseUrl}/api/chat`, {
      message: '有什么职位推荐吗',
      userEmail: '<EMAIL>'
    }, { 
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' }
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`✅ 职位询问成功`);
    console.log(`📊 响应时间: ${responseTime}ms`);
    console.log(`📋 响应摘要:`, {
      success: response.data.success,
      hasResponse: !!response.data.response,
      responseType: response.data.response?.type,
      contentLength: response.data.response?.content?.length || 0
    });
    
    results.push({
      test: '职位询问',
      status: 'SUCCESS',
      responseTime: responseTime,
      data: response.data
    });
    
  } catch (error) {
    console.log(`❌ 职位询问失败: ${error.message}`);
    console.log(`📋 错误详情:`, error.response?.data || 'No response data');
    
    results.push({
      test: '职位询问',
      status: 'FAILURE',
      error: error.message,
      errorData: error.response?.data
    });
  }
  
  // 测试5: 信息提供
  console.log('\n📝 测试5: 信息提供');
  try {
    const startTime = Date.now();
    const response = await axios.post(`${baseUrl}/api/chat`, {
      message: '我是Java开发工程师，有3年经验',
      userEmail: '<EMAIL>'
    }, { 
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' }
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`✅ 信息提供成功`);
    console.log(`📊 响应时间: ${responseTime}ms`);
    console.log(`📋 响应摘要:`, {
      success: response.data.success,
      hasResponse: !!response.data.response,
      responseType: response.data.response?.type,
      contentLength: response.data.response?.content?.length || 0
    });
    
    results.push({
      test: '信息提供',
      status: 'SUCCESS',
      responseTime: responseTime,
      data: response.data
    });
    
  } catch (error) {
    console.log(`❌ 信息提供失败: ${error.message}`);
    console.log(`📋 错误详情:`, error.response?.data || 'No response data');
    
    results.push({
      test: '信息提供',
      status: 'FAILURE',
      error: error.message,
      errorData: error.response?.data
    });
  }
  
  // 生成测试报告
  console.log('\n' + '='.repeat(50));
  console.log('📊 基础测试报告');
  console.log('='.repeat(50));
  
  const totalTests = results.length;
  const successfulTests = results.filter(r => r.status === 'SUCCESS').length;
  const failedTests = results.filter(r => r.status === 'FAILURE').length;
  const successRate = totalTests > 0 ? (successfulTests / totalTests * 100).toFixed(2) : 0;
  
  console.log(`\n📈 测试统计:`);
  console.log(`🧪 总测试数: ${totalTests}`);
  console.log(`✅ 成功测试: ${successfulTests}`);
  console.log(`❌ 失败测试: ${failedTests}`);
  console.log(`📊 成功率: ${successRate}%`);
  
  // 性能统计
  const responseTimes = results
    .filter(r => r.responseTime)
    .map(r => r.responseTime);
  
  if (responseTimes.length > 0) {
    const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const maxTime = Math.max(...responseTimes);
    const minTime = Math.min(...responseTimes);
    
    console.log(`\n⚡ 性能统计:`);
    console.log(`平均响应时间: ${avgTime.toFixed(2)}ms`);
    console.log(`最大响应时间: ${maxTime}ms`);
    console.log(`最小响应时间: ${minTime}ms`);
  }
  
  // 详细结果
  console.log(`\n📝 详细结果:`);
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.test}`);
    console.log(`   状态: ${result.status}`);
    if (result.responseTime) {
      console.log(`   响应时间: ${result.responseTime}ms`);
    }
    if (result.status === 'FAILURE') {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 基础测试完成');
  console.log('='.repeat(50));
}

// 运行测试
if (require.main === module) {
  runBasicTest().catch(error => {
    console.error('❌ 测试执行失败:', error);
  });
}

module.exports = { runBasicTest };
