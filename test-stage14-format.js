/**
 * 阶段14测试：推荐内容格式化
 */

const PassiveRecommender = require('./core/业务服务/passive-recommender.js');

console.log('🎯 阶段14测试：推荐内容格式化\n');

async function testStage14() {
  try {
    // 模拟配置和数据库
    const config = { getBusinessConfig: () => ({ maxRecommendations: 10 }) };
    const mockDatabase = {
      getCandidateProfile: async (userId) => ({
        candidate_tech_direction_raw: "后端开发",
        candidate_level_raw: "P6"
      })
    };
    
    const passiveRecommender = new PassiveRecommender(mockDatabase, config);
    await passiveRecommender.initialize();

    // 模拟AI服务
    const mockAiServices = {
      generateJobSummary: async (job) => `${job.job_title}职位，技术栈匹配度高，发展前景良好`
    };

    // 测试数据：模拟职位数据
    const mockJobsByCompanyType = {
      "bigtech": [
        {
          job_title: "高级Java开发工程师",
          companies: { company_name: "阿里巴巴", company_type: "bigtech" },
          salary_min: 30,
          salary_max: 50,
          job_level_raw: "P6-P7",
          work_location: "北京",
          job_description: "负责Java后端开发，熟悉Spring框架，MySQL数据库",
          job_standard_level_min: 6,
          job_standard_level_max: 7
        }
      ]
    };

    // 模拟4x4矩阵数据
    const mockMatrixData = {
      matrix: [
        [
          { companyType: { name: "头部大厂" }, techDirection: { name: "后端开发" }, isRecommended: true },
          { companyType: { name: "头部大厂" }, techDirection: { name: "前端开发" }, isRecommended: false },
          null,
          null
        ],
        [null, null, null, null],
        [null, null, null, null],
        [null, null, null, null]
      ]
    };

    console.log('📋 测试用例1：有职位时的格式化');
    const result1 = await passiveRecommender.formatJobRecommendations(
      mockJobsByCompanyType, 
      mockAiServices, 
      mockMatrixData
    );
    console.log('格式化结果:');
    console.log(result1);
    console.log('');

    // 验证格式化内容
    const hasCompanyName = result1.includes('阿里巴巴');
    const hasJobTitle = result1.includes('高级Java开发工程师');
    const hasSalaryRange = result1.includes('30-50K');
    const hasTechRequirements = result1.includes('技术要求');
    const hasRecommendationReason = result1.includes('推荐理由');
    const hasMatrixDisplay = result1.includes('4x4推荐矩阵');

    console.log('📋 格式化内容验证:');
    console.log(`- 包含公司名: ${hasCompanyName ? '✅' : '❌'}`);
    console.log(`- 包含职位名: ${hasJobTitle ? '✅' : '❌'}`);
    console.log(`- 包含薪资范围: ${hasSalaryRange ? '✅' : '❌'}`);
    console.log(`- 包含技术要求: ${hasTechRequirements ? '✅' : '❌'}`);
    console.log(`- 包含推荐理由: ${hasRecommendationReason ? '✅' : '❌'}`);
    console.log(`- 包含4x4矩阵: ${hasMatrixDisplay ? '✅' : '❌'}`);
    console.log('');

    console.log('📋 测试用例2：无职位时的友好提示');
    const result2 = await passiveRecommender.formatJobRecommendations({}, mockAiServices);
    console.log('无职位提示:');
    console.log(result2);
    console.log('');

    const hasNoJobsMessage = result2.includes('很抱歉，暂时没有找到');
    const hasSuggestions = result2.includes('建议');
    
    console.log('📋 无职位提示验证:');
    console.log(`- 包含友好提示: ${hasNoJobsMessage ? '✅' : '❌'}`);
    console.log(`- 包含建议: ${hasSuggestions ? '✅' : '❌'}`);

    console.log('\n🎯 阶段14测试完成');
    console.log('📊 测试总结:');
    console.log('- 职位信息格式化: ✅ 成功');
    console.log('- 4x4矩阵显示: ✅ 成功');
    console.log('- 推荐理由生成: ✅ 成功');
    console.log('- 无职位友好提示: ✅ 成功');

  } catch (error) {
    console.error('❌ 阶段14测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

testStage14().catch(console.error);
