{"testId": "integration-test-1753960537990", "startTime": "2025-07-31T11:15:37.990Z", "endTime": "2025-07-31T11:20:39.194Z", "totalTestTime": 301204, "statistics": {"totalTests": 25, "passedTests": 24, "failedTests": 1, "testSuccessRate": 96, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 26367.216216216217, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 26367.216216216217, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:15:38.034Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753960537998, "endTime": 1753960538034, "responseTime": 36, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T11:15:38.028Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:15:46.335Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "18ddb7fb-701d-41b9-9ac9-ee48bb2eff68"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753960538035, "category": "DUAL_MODEL_AI", "endTime": 1753960546333, "responseTime": 8298, "httpStatus": 200, "responseData": {"success": true, "sessionId": "18ddb7fb-701d-41b9-9ac9-ee48bb2eff68", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T11:15:46.329Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:15:51.813Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "43bd9179-bd03-4d57-b16f-394dc3a8006c"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753960547335, "category": "DUAL_MODEL_AI", "endTime": 1753960551813, "responseTime": 4478, "httpStatus": 200, "responseData": {"success": true, "sessionId": "43bd9179-bd03-4d57-b16f-394dc3a8006c", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:15:51.809Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:16:07.432Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "59d643e9-ce25-4bb7-b38e-df816a1d72b0"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753960552815, "category": "DUAL_MODEL_AI", "endTime": 1753960567432, "responseTime": 14617, "httpStatus": 200, "responseData": {"success": true, "sessionId": "59d643e9-ce25-4bb7-b38e-df816a1d72b0", "response": {"type": "first_ai_response", "content": "感谢您分享求职意向。为了更好地了解您的背景，能否请您补充以下信息：\n1. 您具体专注的大模型技术方向（如训练/推理/应用开发等）\n2. 当前职级（如初级/中级/资深工程师等）\n3. 期望的工作地点\n\n（注意：避免主动询问当前公司名称和具体薪资数字）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:16:07.425Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 124, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:16:22.064Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "098ed353-7441-47c6-916a-35ebeffcb9cf"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753960568434, "category": "DUAL_MODEL_AI", "endTime": 1753960582063, "responseTime": 13629, "httpStatus": 200, "responseData": {"success": true, "sessionId": "098ed353-7441-47c6-916a-35ebeffcb9cf", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:16:22.061Z"}, "analysis": {"hasResponse": true, "responseType": "unknown_intent", "contentLength": 33, "featuresDetected": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": true}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:16:27.477Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "6130b560-da5e-4f7f-8967-00b46720ebf1"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960583067, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960587477, "responseTime": 4410, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6130b560-da5e-4f7f-8967-00b46720ebf1", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:16:27.475Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:16:40.013Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "a3319243-9c45-4a50-b8e0-9189c83dc807"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753960588479, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960600013, "responseTime": 11534, "httpStatus": 200, "responseData": {"success": true, "sessionId": "a3319243-9c45-4a50-b8e0-9189c83dc807", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:16:40.009Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:16:56.715Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "1ba43558-5034-41d2-9fa5-c1b4cbc70e8d"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960601017, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960616715, "responseTime": 15698, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1ba43558-5034-41d2-9fa5-c1b4cbc70e8d", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:16:56.707Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:17:10.959Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "ffa34f79-737e-4b26-a0b3-e2900bafa9c3"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753960617717, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960630959, "responseTime": 13242, "httpStatus": 200, "responseData": {"success": true, "sessionId": "ffa34f79-737e-4b26-a0b3-e2900bafa9c3", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "PROFILE_UPDATE", "timestamp": "2025-07-31T11:17:10.954Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:17:25.335Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "9cc58874-13bd-43be-a60b-f66f4fa18bd4"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753960631960, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960645334, "responseTime": 13374, "httpStatus": 200, "responseData": {"success": true, "sessionId": "9cc58874-13bd-43be-a60b-f66f4fa18bd4", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "SALARY_INQUIRY", "timestamp": "2025-07-31T11:17:25.327Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:17:41.231Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "48417d61-8000-4c2a-be12-75294a8e3a5c"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753960646336, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753960661231, "responseTime": 14895, "httpStatus": 200, "responseData": {"success": true, "sessionId": "48417d61-8000-4c2a-be12-75294a8e3a5c", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "GREETING", "timestamp": "2025-07-31T11:17:41.227Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["信息收集引导"], "responseType": "unknown_intent"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:17:46.533Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "409d3654-6970-47a8-ad23-3e298b3a4a78"}, "startTime": 1753960662233, "endTime": 1753960666533, "responseTime": 4300, "httpStatus": 200, "responseData": {"success": true, "sessionId": "409d3654-6970-47a8-ad23-3e298b3a4a78", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T11:17:46.531Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:18:00.319Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "2016c06b-b537-42bc-93dc-44a4b8c3b136"}, "dimension": "technology", "startTime": 1753960666534, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960680319, "responseTime": 13785, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2016c06b-b537-42bc-93dc-44a4b8c3b136", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "RECOMMENDATION_REQUEST", "timestamp": "2025-07-31T11:18:00.313Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:18:12.333Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "83ff9966-e6d7-4d89-9f80-aa1afd4794de"}, "dimension": "experience", "startTime": 1753960681321, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960692333, "responseTime": 11012, "httpStatus": 200, "responseData": {"success": true, "sessionId": "83ff9966-e6d7-4d89-9f80-aa1afd4794de", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:18:12.331Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:18:24.510Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "48a2ea3a-1a88-41c8-8a01-807531a0eac8"}, "dimension": "industry", "startTime": 1753960693335, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960704510, "responseTime": 11175, "httpStatus": 200, "responseData": {"success": true, "sessionId": "48a2ea3a-1a88-41c8-8a01-807531a0eac8", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:18:24.507Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:18:37.544Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "5f5433ba-a681-4794-b5ac-e04c3a809157"}, "dimension": "location", "startTime": 1753960705511, "category": "RECOMMENDATION_MATRIX", "endTime": 1753960717544, "responseTime": 12033, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5f5433ba-a681-4794-b5ac-e04c3a809157", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "JOB_SEARCH", "timestamp": "2025-07-31T11:18:37.539Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:18:50.525Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "52fc1f44-f082-4ad1-b395-c90f35639b3d"}, "startTime": 1753960718546, "endTime": 1753960730525, "responseTime": 11979, "httpStatus": 200, "responseData": {"success": true, "sessionId": "52fc1f44-f082-4ad1-b395-c90f35639b3d", "response": {"type": "first_ai_response", "content": "了解您的需求了。为了更好地帮您分析市场情况，能否先告诉我：\n\n1. 您目前主要的技术栈或专业方向是什么？\n2. 当前的工作年限和职级是？\n\n这些基本信息能帮助我为您提供更有价值的市场参考。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:18:50.522Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:01.611Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "9fd69572-2f31-42b3-88fb-1ce5026d745c"}, "startTime": 1753960730526, "category": "CONVERSATION_TERMINATION", "endTime": 1753960741611, "responseTime": 11085, "httpStatus": 200, "responseData": {"success": true, "sessionId": "9fd69572-2f31-42b3-88fb-1ce5026d745c", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:19:01.607Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:13.482Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "1f6f7ceb-509b-468d-8286-b2c1904cb0db"}, "startTime": 1753960742612, "category": "CONVERSATION_TERMINATION", "endTime": 1753960753481, "responseTime": 10869, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1f6f7ceb-509b-468d-8286-b2c1904cb0db", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:19:13.478Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:27.274Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "cdca4cff-83d7-4ffb-be08-b49df9c2152b"}, "startTime": 1753960754482, "category": "CONVERSATION_TERMINATION", "endTime": 1753960767273, "responseTime": 12791, "httpStatus": 200, "responseData": {"success": true, "sessionId": "cdca4cff-83d7-4ffb-be08-b49df9c2152b", "response": {"type": "first_ai_response", "content": "我理解您现在的想法。不过为了后续如果有机会时能更好地为您服务，能否简单告诉我：\n\n1. 您主要的技术方向或专长领域是什么？\n2. 目前的工作年限和职级是？\n\n这样我可以先帮您完善档案信息。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:19:27.271Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:28.285Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "dc183eb3-e887-4583-8d3f-cb0b1e989344"}, "startTime": 1753960768275, "category": "ERROR_HANDLING", "endTime": 1753960768285, "responseTime": 10, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:40.219Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "b9bf645e-a059-4814-9e67-f193c06f5087"}, "startTime": 1753960769286, "category": "ERROR_HANDLING", "endTime": 1753960780219, "responseTime": 10933, "httpStatus": 200, "responseData": {"success": true, "sessionId": "b9bf645e-a059-4814-9e67-f193c06f5087", "response": {"type": "unknown_intent", "content": "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"]}, "intent": "UNKNOWN", "timestamp": "2025-07-31T11:19:40.216Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:41.225Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "14f53adf-6f43-4898-a2e4-8cc1bfce7f81"}, "startTime": 1753960781220, "category": "ERROR_HANDLING", "endTime": 1753960781224, "responseTime": 4, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T11:19:41.224Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:19:53.912Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "00832dd4-3d92-4147-9fb9-964a3e6473c9"}, "startTime": 1753960782226, "category": "ERROR_HANDLING", "endTime": 1753960793912, "responseTime": 11686, "httpStatus": 200, "responseData": {"success": true, "sessionId": "00832dd4-3d92-4147-9fb9-964a3e6473c9", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina。为了帮您更精准地匹配机会，请提供以下信息：\n1. 您的技术栈或专业方向\n2. 当前职级（如初级/中级/高级工程师等）\n3. 期望的工作地点\n\n（注意：我不会推荐具体职位，但会根据您提供的信息给予求职建议）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T11:19:53.909Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753960537990", "timestamp": "2025-07-31T11:20:39.193Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753960794914, "endTime": 1753960839192, "responseTime": 44278, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}