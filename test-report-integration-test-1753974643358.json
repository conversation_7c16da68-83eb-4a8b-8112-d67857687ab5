{"testId": "integration-test-1753974643358", "startTime": "2025-07-31T15:10:43.358Z", "endTime": "2025-07-31T15:16:26.605Z", "totalTestTime": 343247, "statistics": {"totalTests": 25, "passedTests": 20, "failedTests": 5, "testSuccessRate": 80, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 28268.202702702703, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 28268.202702702703, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:10:43.414Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753974643366, "endTime": 1753974643414, "responseTime": 48, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T15:10:43.410Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:10:50.217Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "9d840455-f2ba-4be8-ae91-91cb7f908e50"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753974643415, "category": "DUAL_MODEL_AI", "endTime": 1753974650216, "responseTime": 6801, "httpStatus": 200, "responseData": {"success": true, "sessionId": "9d840455-f2ba-4be8-ae91-91cb7f908e50", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T15:10:50.212Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:10:55.409Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "07437b8d-ad19-47d4-b749-daa8243de484"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753974651218, "category": "DUAL_MODEL_AI", "endTime": 1753974655409, "responseTime": 4191, "httpStatus": 200, "responseData": {"success": true, "sessionId": "07437b8d-ad19-47d4-b749-daa8243de484", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T15:10:55.404Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:10.188Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "943a5f7f-1fb4-4c28-bdf5-10af7f753e5a"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753974656411, "category": "DUAL_MODEL_AI", "endTime": 1753974670188, "responseTime": 13777, "httpStatus": 200, "responseData": {"success": true, "sessionId": "943a5f7f-1fb4-4c28-bdf5-10af7f753e5a", "response": {"type": "first_ai_response", "content": "感谢您分享求职意向！大模型方向确实是非常前沿的领域。为了更好地为您推荐合适的机会，能否请您补充以下关键信息：\n1. 您主要使用的技术栈（如Python/PyTorch等）\n2. 当前职级和工作年限\n3. 期望的工作地点和薪资范围？\n\n这些信息将帮助我为您提供更精准的职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T15:11:10.183Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 138, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:22.220Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "e6c1d46c-ec59-4269-9bf5-6f40f13cadaa"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753974671189, "category": "DUAL_MODEL_AI", "endTime": 1753974682220, "responseTime": 11031, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e6c1d46c-ec59-4269-9bf5-6f40f13cadaa", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T15:11:21.353Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T15:11:22.213Z"}, "analysis": {"hasResponse": true, "responseType": "error", "contentLength": 21, "featuresDetected": ["技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": false}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "FAILED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:30.010Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "b06e616c-0ae1-47a8-9f40-81a918d25278"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753974683223, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974690009, "responseTime": 6786, "httpStatus": 200, "responseData": {"success": true, "sessionId": "b06e616c-0ae1-47a8-9f40-81a918d25278", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T15:11:30.006Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:39.966Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "1bca615b-1c8e-4e64-bed5-a2ad773ac218"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753974691011, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974699966, "responseTime": 8955, "httpStatus": 200, "responseData": {"success": true, "sessionId": "1bca615b-1c8e-4e64-bed5-a2ad773ac218", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T15:11:38.959Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T15:11:39.962Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:48.089Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "81717991-da8f-451a-9723-cb6fa31eee88"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753974700968, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974708089, "responseTime": 7121, "httpStatus": 200, "responseData": {"success": true, "sessionId": "81717991-da8f-451a-9723-cb6fa31eee88", "response": {"type": "professional_decline", "content": "我了解您在Python开发工程师方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有Python开发工程师的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "Python开发工程师"}}}, "intent": "profile_update", "timestamp": "2025-07-31T15:11:48.086Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["了解您"], "responseType": "professional_decline"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:11:55.936Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "e6771ec3-5bb8-411a-a64d-40559be88e54"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753974709091, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974715936, "responseTime": 6845, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e6771ec3-5bb8-411a-a64d-40559be88e54", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T15:11:54.730Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T15:11:55.933Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:04.443Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "44045d6b-bdcb-4b04-90e5-09d6e28eb972"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753974716937, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974724443, "responseTime": 7506, "httpStatus": 200, "responseData": {"success": true, "sessionId": "44045d6b-bdcb-4b04-90e5-09d6e28eb972", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T15:12:02.899Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T15:12:04.438Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:25.456Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "bb8493b2-66bf-415f-938f-7612548c125d"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753974725445, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753974745456, "responseTime": 20011, "httpStatus": 200, "responseData": {"success": true, "sessionId": "bb8493b2-66bf-415f-938f-7612548c125d", "response": {"type": "first_ai_response", "content": "您好！很高兴在这个好天气与您交流。作为专业的招聘顾问，为了更好地为您推荐合适的职位机会，能否请您简单分享以下信息：\n1. 您的技术栈或专业方向\n2. 当前职级和工作经验\n3. 期望的工作地点和薪资范围？\n\n这样我可以更有针对性地为您提供职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "greeting", "timestamp": "2025-07-31T15:12:25.450Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["您的技术", "工作经验", "技术栈", "为了更好", "能否请您", "分享以下", "推荐合适的职位"], "responseType": "first_ai_response"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:35.757Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "e83a5e6f-cf97-4096-b153-faa7fa974d8d"}, "startTime": 1753974746459, "endTime": 1753974755757, "responseTime": 9298, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e83a5e6f-cf97-4096-b153-faa7fa974d8d", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T15:12:35.754Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:42.964Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "922e7c32-c719-4a8f-922b-6bf7b60b038a"}, "dimension": "technology", "startTime": 1753974755758, "category": "RECOMMENDATION_MATRIX", "endTime": 1753974762964, "responseTime": 7206, "httpStatus": 200, "responseData": {"success": true, "sessionId": "922e7c32-c719-4a8f-922b-6bf7b60b038a", "response": {"type": "professional_decline", "content": "我了解您在Java、Spring Boot、MySQL方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有Java、Spring Boot、MySQL的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "Java、Spring Boot、MySQL"}}}, "intent": "profile_update", "timestamp": "2025-07-31T15:12:42.959Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:50.808Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "dd96280a-aa63-438e-bb10-812e733902da"}, "dimension": "experience", "startTime": 1753974763966, "category": "RECOMMENDATION_MATRIX", "endTime": 1753974770807, "responseTime": 6841, "httpStatus": 200, "responseData": {"success": true, "sessionId": "dd96280a-aa63-438e-bb10-812e733902da", "response": {"type": "professional_decline", "content": "我了解您在开发方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有开发的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "开发", "工作经验": "5年", "当前职级": "高级"}}}, "intent": "profile_update", "timestamp": "2025-07-31T15:12:50.802Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:12:57.792Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "91356f6f-c9a7-4eaf-81af-bca62ffe6670"}, "dimension": "industry", "startTime": 1753974771808, "category": "RECOMMENDATION_MATRIX", "endTime": 1753974777792, "responseTime": 5984, "httpStatus": 200, "responseData": {"success": true, "sessionId": "91356f6f-c9a7-4eaf-81af-bca62ffe6670", "response": {"type": "professional_decline", "content": "我了解您在金融科技领域方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有金融科技领域的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "金融科技领域", "业务场景": "金融"}}}, "intent": "profile_update", "timestamp": "2025-07-31T15:12:57.789Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:13:06.025Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "cfa1ede8-0a78-4c6f-aae2-56bce3a3772f"}, "dimension": "location", "startTime": 1753974778793, "category": "RECOMMENDATION_MATRIX", "endTime": 1753974786025, "responseTime": 7232, "httpStatus": 200, "responseData": {"success": true, "sessionId": "cfa1ede8-0a78-4c6f-aae2-56bce3a3772f", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T15:13:05.179Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T15:13:06.019Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:13:22.492Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "69dd3e08-a02e-410a-846c-821c592e6d75"}, "startTime": 1753974787028, "endTime": 1753974802492, "responseTime": 15464, "httpStatus": 200, "responseData": {"success": true, "sessionId": "69dd3e08-a02e-410a-846c-821c592e6d75", "response": {"type": "first_ai_response", "content": "感谢您愿意了解市场情况！为了更好地为您提供参考，能否分享一些基本信息：\n1. 您目前主要的技术栈或技术方向是什么？\n2. 当前所在公司类型及职级？\n3. 对工作地点或薪资范围是否有初步想法？\n\n这些信息能帮助我为您提供更有针对性的市场分析。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T15:13:22.487Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:13:52.932Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "ced3ec4c-dcd4-44a8-8191-3711fd64e494"}, "startTime": 1753974802493, "category": "CONVERSATION_TERMINATION", "endTime": 1753974832932, "responseTime": 30439, "httpStatus": 200, "responseData": {"success": true, "sessionId": "ced3ec4c-dcd4-44a8-8191-3711fd64e494", "response": {"type": "ai_understanding", "content": "我完全理解您的决定，Katrina。作为专业的AI招聘助手，我的职责是尊重每一位求职者的选择。  \n\n如果您今后在职业发展或AI算法岗位方面有任何疑问，比如想了解行业趋势、面试准备建议或职业规划等，随时欢迎回来咨询。我会继续以专业客观的态度为您提供支持。  \n\n祝您职业发展顺利！", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "谢谢，我不需要更多信息了"}}, "intent": "unknown", "timestamp": "2025-07-31T15:13:52.926Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:14:17.150Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "82cd2408-10bc-4c58-aec2-332d099d45db"}, "startTime": 1753974833933, "category": "CONVERSATION_TERMINATION", "endTime": 1753974857150, "responseTime": 23217, "httpStatus": 200, "responseData": {"success": true, "sessionId": "82cd2408-10bc-4c58-aec2-332d099d45db", "response": {"type": "ai_understanding", "content": "您好Katrina！我理解您可能对当前的求职方向还不太确定。作为专业的AI招聘助手，我可以帮助您梳理技术背景和职业目标。\n\n为了更好地帮助您，请问：\n1. 您目前从事哪个技术领域？（比如算法开发、数据分析等）\n2. 您有几年相关工作经验？\n\n这样我可以为您提供更有针对性的职业建议。", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "不知道"}}, "intent": "unknown", "timestamp": "2025-07-31T15:14:17.145Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:14:31.007Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "725630c2-6aca-4a93-8c2a-35e8451a8b2c"}, "startTime": 1753974858152, "category": "CONVERSATION_TERMINATION", "endTime": 1753974871006, "responseTime": 12854, "httpStatus": 200, "responseData": {"success": true, "sessionId": "725630c2-6aca-4a93-8c2a-35e8451a8b2c", "response": {"type": "first_ai_response", "content": "我理解您当前可能对求职有些犹豫。为了更好地评估您的职业发展机会，能否先告诉我：  \n1. 您的技术栈或主要技术方向是什么？  \n2. 目前在什么类型的公司/行业工作？  \n\n这样我可以帮您分析潜在的市场机会。（专业而温和地引导关键信息）", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T15:14:31.001Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:14:32.025Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "68fece66-8a2b-4c3e-a1d1-675dbe8d5dd6"}, "startTime": 1753974872009, "category": "ERROR_HANDLING", "endTime": 1753974872025, "responseTime": 16, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:15:13.350Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "c9523168-1fad-4c9e-9ff2-10519f584217"}, "startTime": 1753974873027, "category": "ERROR_HANDLING", "endTime": 1753974913350, "responseTime": 40323, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c9523168-1fad-4c9e-9ff2-10519f584217", "response": {"type": "ai_understanding", "content": "您好，我注意到您发送了一段重复的字符信息。作为专业的AI招聘助手，我可能需要您提供更多关于求职需求的具体信息，这样我才能更好地为您服务。\n\n为了更好地帮助您，请问您是否可以告诉我：\n1. 您的技术方向或专业领域是什么？（例如：Java开发、算法工程师等）\n2. 您目前的工作经验年限？\n3. 您期望的工作地点和薪资范围？\n\n这样我就能为您提供更有针对性的职业建议和帮助了。", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"}}, "intent": "unknown", "timestamp": "2025-07-31T15:15:13.344Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:15:14.364Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "738a6d11-c1df-41ee-8420-00038508f948"}, "startTime": 1753974914352, "category": "ERROR_HANDLING", "endTime": 1753974914364, "responseTime": 12, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T15:15:14.358Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:15:32.995Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "7c73011d-0718-4191-b4a5-8d31903912bb"}, "startTime": 1753974915365, "category": "ERROR_HANDLING", "endTime": 1753974932995, "responseTime": 17630, "httpStatus": 200, "responseData": {"success": true, "sessionId": "7c73011d-0718-4191-b4a5-8d31903912bb", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina，很高兴为您服务。为了更好地为您推荐合适的职位，能否请您分享以下信息：\n1. 您的技术栈或专业方向（如Java/Python/前端等）\n2. 当前职级和工作年限\n3. 期望的工作地点和薪资范围\n\n这些信息将帮助我为您提供更精准的职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T15:15:32.992Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753974643358", "timestamp": "2025-07-31T15:16:26.605Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753974933997, "endTime": 1753974986604, "responseTime": 52607, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}