/**
 * AI招聘助手系统 - 聊天界面组件
 *
 * 核心职责：
 * - 前端聊天界面
 * - 用户交互处理
 * - 消息显示管理
 * - 界面状态控制
 *
 * 预计代码量：1600行
 */

class ChatInterface {
  constructor(containerId, config = {}) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.config = {
      apiBaseUrl: config.apiBaseUrl || "/api",
      userEmail: config.userEmail || "",
      sessionId: config.sessionId || null,
      theme: config.theme || "light",
      ...config,
    };

    this.sessionId = this.config.sessionId;
    this.isLoading = false;
    this.messageHistory = [];
    this.sendingStatus = false; // 消息发送状态

    // 从URL获取token参数（支持跨设备同步）
    this.initFromUrlToken();

    this.init();
  }

  /**
   * 从URL Token初始化会话（支持跨设备同步）
   */
  initFromUrlToken() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");

    if (token) {
      // 使用token作为sessionId实现跨设备同步
      this.sessionId = token;
      this.config.sessionId = token;
    }
  }

  /**
   * 初始化聊天界面
   */
  init() {
    this.createChatInterface();
    this.bindEvents();
    this.loadChatHistory();

    // 如果没有会话ID，创建新会话
    if (!this.sessionId) {
      this.createNewSession();
    }
  }

  /**
   * 创建聊天界面HTML结构
   */
  createChatInterface() {
    this.container.innerHTML = `
      <div class="chat-container ${this.config.theme}">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="chat-title">
            <h3>AI招聘助手 Katrina</h3>
            <span class="status-indicator online">在线</span>
          </div>
          <div class="chat-actions">
            <button class="btn-clear" title="清空对话">🗑️</button>
            <button class="btn-settings" title="设置">⚙️</button>
          </div>
        </div>

        <!-- 消息显示区域 -->
        <div class="chat-messages" id="chatMessages">
          <div class="welcome-message">
            <div class="message assistant">
              <div class="message-avatar">🤖</div>
              <div class="message-content">
                <p>你好！我是Katrina，您的AI招聘助手。我可以帮您：</p>
                <ul>
                  <li>🎯 推荐合适的职位</li>
                  <li>💰 了解薪资行情</li>
                  <li>🏢 分析公司信息</li>
                  <li>📝 优化个人档案</li>
                </ul>
                <p>请告诉我您的需求，让我们开始吧！</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷建议 -->
        <div class="quick-suggestions" id="quickSuggestions">
          <div class="suggestion-item" data-text="我想找工作">我想找工作</div>
          <div class="suggestion-item" data-text="推荐职位给我">推荐职位给我</div>
          <div class="suggestion-item" data-text="了解薪资行情">了解薪资行情</div>
          <div class="suggestion-item" data-text="更新个人信息">更新个人信息</div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
          <div class="input-container">
            <textarea 
              id="messageInput" 
              placeholder="输入您的消息..." 
              rows="1"
              maxlength="2000"
            ></textarea>
            <button id="sendButton" class="send-button" disabled>
              <span class="send-icon">📤</span>
            </button>
          </div>
          <div class="input-footer">
            <span class="char-count">0/2000</span>
            <span class="sending-status" style="display: none;">正在发送...</span>
            <span class="typing-indicator" style="display: none;">正在输入...</span>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-indicator" id="loadingIndicator" style="display: none;">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span>Katrina正在思考...</span>
        </div>
      </div>
    `;

    this.addStyles();
  }

  /**
   * 添加样式
   */
  addStyles() {
    if (document.getElementById("chatInterfaceStyles")) return;

    const styles = `
      <style id="chatInterfaceStyles">
        .chat-container {
          display: flex;
          flex-direction: column;
          height: 600px;
          max-width: 800px;
          margin: 0 auto;
          border: 1px solid #e1e5e9;
          border-radius: 12px;
          background: #ffffff;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #e1e5e9;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px 12px 0 0;
        }

        .chat-title h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .status-indicator {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          margin-left: 8px;
        }

        .status-indicator.online {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
        }

        .chat-actions button {
          background: none;
          border: none;
          color: white;
          font-size: 16px;
          cursor: pointer;
          padding: 8px;
          border-radius: 6px;
          margin-left: 8px;
          transition: background-color 0.2s;
        }

        .chat-actions button:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 20px;
          background: #f8f9fa;
        }

        .message {
          display: flex;
          margin-bottom: 16px;
          animation: fadeInUp 0.3s ease-out;
        }

        .message.user {
          flex-direction: row-reverse;
        }

        .message-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          margin: 0 12px;
          flex-shrink: 0;
        }

        .message.assistant .message-avatar {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .message.user .message-avatar {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .message-content {
          max-width: 70%;
          padding: 12px 16px;
          border-radius: 18px;
          line-height: 1.4;
        }

        .message.assistant .message-content {
          background: white;
          border: 1px solid #e1e5e9;
          border-bottom-left-radius: 6px;
        }

        .message.user .message-content {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-bottom-right-radius: 6px;
        }

        .quick-suggestions {
          display: flex;
          gap: 8px;
          padding: 12px 20px;
          overflow-x: auto;
          border-top: 1px solid #e1e5e9;
        }

        .suggestion-item {
          background: #f1f3f4;
          border: 1px solid #dadce0;
          border-radius: 20px;
          padding: 8px 16px;
          cursor: pointer;
          white-space: nowrap;
          font-size: 14px;
          transition: all 0.2s;
        }

        .suggestion-item:hover {
          background: #e8f0fe;
          border-color: #4285f4;
          color: #1a73e8;
        }

        .chat-input-area {
          border-top: 1px solid #e1e5e9;
          padding: 16px 20px;
          background: white;
          border-radius: 0 0 12px 12px;
        }

        .input-container {
          display: flex;
          align-items: flex-end;
          gap: 12px;
        }

        #messageInput {
          flex: 1;
          border: 1px solid #dadce0;
          border-radius: 20px;
          padding: 12px 16px;
          font-size: 14px;
          resize: none;
          outline: none;
          transition: border-color 0.2s;
          max-height: 120px;
        }

        #messageInput:focus {
          border-color: #4285f4;
        }

        .send-button {
          width: 40px;
          height: 40px;
          border: none;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: transform 0.2s;
        }

        .send-button:hover:not(:disabled) {
          transform: scale(1.05);
        }

        .send-button:disabled {
          background: #dadce0;
          cursor: not-allowed;
        }

        .input-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;
          font-size: 12px;
          color: #5f6368;
        }

        .sending-status { color: #1a73e8; font-weight: 500; }
        .message-content.long-message { max-height: 200px; overflow-y: auto; border: 1px solid #e1e5e9; border-radius: 8px; padding: 12px; }
        .job-recommendation { border: 1px solid #e8f0fe; border-radius: 8px; padding: 12px; margin: 8px 0; background: #f8f9ff; cursor: pointer; transition: all 0.2s; }
        .job-recommendation:hover { background: #e8f0fe; border-color: #4285f4; transform: translateY(-1px); }
        .error-message { background: #fef7f0; border: 1px solid #fce8e6; border-radius: 8px; padding: 12px; color: #d93025; }
        .error-message .error-icon { margin-right: 8px; }

        .loading-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16px;
          background: rgba(255, 255, 255, 0.9);
          border-top: 1px solid #e1e5e9;
        }

        .loading-dots {
          display: flex;
          gap: 4px;
          margin-right: 8px;
        }

        .loading-dots span {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #4285f4;
          animation: loadingDots 1.4s infinite ease-in-out;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loadingDots {
          0%, 80%, 100% { transform: scale(0); }
          40% { transform: scale(1); }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      </style>
    `;

    document.head.insertAdjacentHTML("beforeend", styles);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    const messageInput = document.getElementById("messageInput");
    const sendButton = document.getElementById("sendButton");
    const quickSuggestions = document.getElementById("quickSuggestions");

    // 发送按钮点击事件
    sendButton.addEventListener("click", () => this.sendMessage());

    // 输入框事件
    messageInput.addEventListener("input", (e) => {
      this.updateCharCount();
      this.updateSendButton();
      this.autoResize(e.target);
    });

    messageInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });

    // 快捷建议点击事件
    quickSuggestions.addEventListener("click", (e) => {
      if (e.target.classList.contains("suggestion-item")) {
        const text = e.target.dataset.text;
        messageInput.value = text;
        this.updateCharCount();
        this.updateSendButton();
        messageInput.focus();
      }
    });

    // 清空对话按钮
    document.querySelector(".btn-clear").addEventListener("click", () => {
      this.clearChat();
    });
  }

  /**
   * 发送消息
   */
  async sendMessage() {
    const messageInput = document.getElementById("messageInput");
    const message = messageInput.value.trim();

    if (!message || this.isLoading) return;

    // 显示发送状态
    this.showSendingStatus(true);

    // 显示用户消息
    this.addMessage("user", message);

    // 清空输入框
    messageInput.value = "";
    this.updateCharCount();
    this.updateSendButton();
    this.autoResize(messageInput);

    // 显示加载状态
    this.showLoading(true);

    try {
      console.log("🚀 开始发送消息:", message);

      // 发送到后端
      const response = await this.sendToAPI(message);
      console.log("📨 收到完整响应:", response);

      // 显示助手回复
      this.addMessage("assistant", response.response);
      console.log("💬 已添加助手消息");

      // 检查是否有后续消息需要显示
      if (
        response.response.metadata &&
        response.response.metadata.hasFollowUpMessage
      ) {
        console.log("✅ 检测到hasFollowUpMessage，将在2秒后获取后续消息");
        // 2秒后显示第二条消息
        setTimeout(async () => {
          try {
            console.log("⏰ 2秒后开始获取后续消息...");
            // 获取第二条消息
            const followUpResponse = await this.getFollowUpMessage();
            console.log("📨 获取到后续消息:", followUpResponse);
            if (followUpResponse && followUpResponse.content) {
              console.log("✅ 显示后续消息:", followUpResponse.content);
              this.addMessage("assistant", {
                type: "follow_up",
                content: followUpResponse.content,
              });
            } else {
              console.log("❌ 没有获取到有效的后续消息");
            }
          } catch (error) {
            console.error("获取后续消息失败:", error);
          }
        }, 2000);
      }

      // 更新快捷建议
      if (response.response.suggestions) {
        this.updateQuickSuggestions(response.response.suggestions);
      }
    } catch (error) {
      console.error("发送消息失败:", error);
      this.addMessage("assistant", {
        type: "error",
        content: this.getErrorMessage(error),
      });
    } finally {
      this.showLoading(false);
      this.showSendingStatus(false);
    }
  }

  /**
   * 发送消息到API
   */
  async sendToAPI(message) {
    const response = await fetch(`${this.config.apiBaseUrl}/chat/message`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message: message,
        sessionId: this.sessionId,
        userEmail: this.config.userEmail,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // 更新会话ID
    if (data.sessionId) {
      this.sessionId = data.sessionId;
    }

    return data;
  }

  /**
   * 获取后续消息
   */
  async getFollowUpMessage() {
    try {
      const response = await fetch(`${this.config.apiBaseUrl}/chat/follow-up`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sessionId: this.sessionId,
          userEmail: this.config.userEmail,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error("获取后续消息失败:", error);
      return null;
    }
  }

  /**
   * 添加消息到界面
   */
  addMessage(type, content) {
    const messagesContainer = document.getElementById("chatMessages");
    const messageElement = document.createElement("div");
    messageElement.className = `message ${type}`;

    const avatar = type === "user" ? "👤" : "🤖";
    const messageContent =
      typeof content === "string" ? content : content.content;

    messageElement.innerHTML = `
      <div class="message-avatar">${avatar}</div>
      <div class="message-content">
        ${this.formatMessageContent(messageContent)}
      </div>
    `;

    messagesContainer.appendChild(messageElement);
    this.scrollToBottom();

    // 保存到历史记录
    this.messageHistory.push({
      type: type,
      content: messageContent,
      timestamp: new Date().toISOString(),
    });
  }

  // 格式化消息内容
  formatMessageContent(content) {
    if (!content) return "";
    if (typeof content === "object" && content.type === "error")
      return `<div class="error-message"><span class="error-icon">⚠️</span>${content.content}</div>`;
    if (typeof content === "string" && content.includes("职位推荐"))
      return this.formatJobRecommendation(content);
    const messageText =
      typeof content === "string" ? content : content.content || "";
    const formattedContent = messageText.replace(/\n/g, "<br>");
    return messageText.length > 300
      ? `<div class="message-content long-message">${formattedContent}</div>`
      : formattedContent;
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    const messagesContainer = document.getElementById("chatMessages");
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const loadingIndicator = document.getElementById("loadingIndicator");
    const sendButton = document.getElementById("sendButton");

    this.isLoading = show;
    loadingIndicator.style.display = show ? "flex" : "none";
    sendButton.disabled = show;
  }

  /**
   * 更新字符计数
   */
  updateCharCount() {
    const messageInput = document.getElementById("messageInput");
    const charCount = document.querySelector(".char-count");
    const length = messageInput.value.length;

    charCount.textContent = `${length}/2000`;
    charCount.style.color = length > 1800 ? "#ea4335" : "#5f6368";
  }

  /**
   * 更新发送按钮状态
   */
  updateSendButton() {
    const messageInput = document.getElementById("messageInput");
    const sendButton = document.getElementById("sendButton");

    sendButton.disabled = !messageInput.value.trim() || this.isLoading;
  }

  /**
   * 自动调整输入框高度
   */
  autoResize(textarea) {
    textarea.style.height = "auto";
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";
  }

  /**
   * 更新快捷建议
   */
  updateQuickSuggestions(suggestions) {
    const quickSuggestions = document.getElementById("quickSuggestions");

    if (suggestions && suggestions.length > 0) {
      quickSuggestions.innerHTML = suggestions
        .map(
          (suggestion) =>
            `<div class="suggestion-item" data-text="${suggestion}">${suggestion}</div>`
        )
        .join("");
    }
  }

  /**
   * 清空对话
   */
  clearChat() {
    if (confirm("确定要清空对话记录吗？")) {
      const messagesContainer = document.getElementById("chatMessages");
      messagesContainer.innerHTML = "";
      this.messageHistory = [];
      this.createNewSession();
    }
  }

  /**
   * 创建新会话
   */
  async createNewSession() {
    try {
      const response = await fetch(`${this.config.apiBaseUrl}/chat/session`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userEmail: this.config.userEmail,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.sessionId = data.sessionId;
      }
    } catch (error) {
      console.error("创建会话失败:", error);
    }
  }

  /**
   * 加载聊天历史
   */
  async loadChatHistory() {
    if (!this.sessionId) return;

    try {
      const response = await fetch(
        `${this.config.apiBaseUrl}/chat/history/${this.sessionId}`
      );

      if (response.ok) {
        const data = await response.json();

        if (data.messages && data.messages.length > 0) {
          // 清空欢迎消息
          const messagesContainer = document.getElementById("chatMessages");
          messagesContainer.innerHTML = "";

          // 显示历史消息
          data.messages.forEach((msg) => {
            this.addMessage(msg.message_type, msg.message_content);
          });
        }
      }
    } catch (error) {
      console.error("加载聊天历史失败:", error);
    }
  }

  /**
   * 获取当前会话ID
   */
  getSessionId() {
    return this.sessionId;
  }

  /**
   * 获取消息历史
   */
  getMessageHistory() {
    return this.messageHistory;
  }

  // 显示/隐藏发送状态
  showSendingStatus(show) {
    const sendingStatus = document.querySelector(".sending-status");
    this.sendingStatus = show;
    if (sendingStatus) sendingStatus.style.display = show ? "inline" : "none";
  }
  // 格式化职位推荐
  formatJobRecommendation(content) {
    return `<div class="job-recommendation" onclick="this.handleJobClick(this)">${content}</div>`;
  }
  // 处理职位点击
  handleJobClick(element) {
    console.log("点击职位:", element.textContent);
    element.style.background = "#e8f0fe";
  }
  // 获取友好的错误消息
  getErrorMessage(error) {
    if (error.message.includes("fetch"))
      return "网络连接出现问题，请检查您的网络连接后重试。";
    if (error.message.includes("500")) return "服务器暂时繁忙，请稍后再试。";
    if (error.message.includes("404"))
      return "请求的服务不存在，请联系技术支持。";
    return "抱歉，我遇到了一些技术问题。请稍后再试。";
  }
}
// 如果在浏览器环境中，将类添加到全局对象
if (typeof window !== "undefined") {
  window.ChatInterface = ChatInterface;
}
module.exports = ChatInterface;
