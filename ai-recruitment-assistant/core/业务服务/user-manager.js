/**
 * AI招聘助手系统 - 用户管理器
 *
 * 核心职责：
 * - 用户认证和授权
 * - 会话管理
 * - 用户档案维护
 * - 权限控制
 *
 * 预计代码量：1200行
 */

class UserManager {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * 初始化用户管理器
   */
  async initialize() {
    try {
      this.isInitialized = true;
      console.log("👤 用户管理器初始化完成");
    } catch (error) {
      console.error("❌ 用户管理器初始化失败:", error);
      throw error;
    }
  }

  /**
   * 获取或创建用户
   */
  async getOrCreateUser(email) {
    try {
      // 尝试获取现有用户
      let user = await this.database.getUserByEmail(email);

      if (!user) {
        // 创建新用户
        user = await this.database.createUser({
          email: email,
          userType: "candidate",
        });

        console.log("✅ 新用户创建成功:", email);
      } else {
        // 更新最后登录时间
        await this.database.updateUserLastLogin(user.id);
      }

      return user;
    } catch (error) {
      console.error("❌ 获取或创建用户失败:", error);
      throw error;
    }
  }

  /**
   * 根据会话获取用户
   */
  async getUserBySession(session) {
    try {
      const { data, error } = await this.database.client
        .from("users")
        .select("*")
        .eq("id", session.user_id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("❌ 根据会话获取用户失败:", error);
      throw error;
    }
  }

  /**
   * 验证用户权限
   */
  async validateUserPermission(userId, permission) {
    try {
      // 基础权限验证逻辑
      // 这里可以扩展为更复杂的权限系统
      return true;
    } catch (error) {
      console.error("❌ 验证用户权限失败:", error);
      return false;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(userId) {
    try {
      // 获取用户的各种统计信息
      // 如：会话数量、消息数量、推荐数量等
      return {
        totalSessions: 0,
        totalMessages: 0,
        totalRecommendations: 0,
      };
    } catch (error) {
      console.error("❌ 获取用户统计信息失败:", error);
      return {};
    }
  }

  // ==================== 用户信息状态管理 ====================

  /**
   * 获取用户信息收集状态
   */
  async getUserInfoState(userId) {
    try {
      const profile = await this.database.getCandidateProfile(userId);

      if (!profile) {
        return {
          所在公司: null,
          技术方向: null,
          当前职级: null,
          期望薪资: null,
          所在城市: null,
          业务场景: null,
          收集进度: 0,
          可推荐: false,
        };
      }

      const infoState = {
        所在公司: profile.current_company_name_raw || null,
        技术方向: profile.candidate_tech_direction_raw || null,
        当前职级: profile.candidate_level_raw || null,
        期望薪资: profile.expected_compensation_raw || null,
        所在城市: profile.desired_location_raw || null,
        业务场景: profile.candidate_business_scenario_raw || null,
      };

      // 计算收集进度
      const collectedCount = Object.values(infoState).filter(
        (v) => v !== null
      ).length;
      infoState.收集进度 = collectedCount;

      // 判断是否可以推荐（4*4逻辑的触发条件）
      infoState.可推荐 = this.checkRecommendationEligibility(infoState);

      return infoState;
    } catch (error) {
      console.error("❌ 获取用户信息状态失败:", error);
      return null;
    }
  }

  /**
   * 更新用户信息状态
   */
  async updateUserInfoState(userId, infoUpdates) {
    try {
      // 使用upsert方法创建或更新档案
      const updates = this.mapInfoToProfile(infoUpdates);
      const profile = await this.database.upsertCandidateProfile(
        userId,
        updates
      );

      console.log(
        `✅ 用户信息状态更新成功: ${Object.keys(infoUpdates).join(", ")}`
      );

      // 返回更新后的状态
      return await this.getUserInfoState(userId);
    } catch (error) {
      console.error("❌ 更新用户信息状态失败:", error);
      throw error;
    }
  }

  /**
   * 检查推荐资格（4*4逻辑触发条件）
   */
  checkRecommendationEligibility(infoState) {
    const { 所在公司, 技术方向, 当前职级, 期望薪资 } = infoState;

    // 基础验证：必须有公司和技术方向
    if (!所在公司 || !技术方向) {
      return { isEligible: false, triggerType: null };
    }

    // 职级有效性检查（职级在对照表中存在）
    const isValidJobLevel = this.validateJobLevel(当前职级);

    // 条件3：公司+技术+职级+期望薪酬（完整信息）
    if (所在公司 && 技术方向 && 当前职级 && 期望薪资 && isValidJobLevel) {
      return { isEligible: true, triggerType: "complete_info" };
    }

    // 条件1：公司+技术+职级（职级在对照表中存在）
    if (所在公司 && 技术方向 && 当前职级 && isValidJobLevel) {
      return { isEligible: true, triggerType: "company_tech_level" };
    }

    // 条件2：公司+技术+期望薪酬
    if (所在公司 && 技术方向 && 期望薪资) {
      return { isEligible: true, triggerType: "company_tech_salary" };
    }

    // 职级无效时的兜底逻辑（必须有薪酬）
    if (所在公司 && 技术方向 && 当前职级 && !isValidJobLevel && 期望薪资) {
      return { isEligible: true, triggerType: "fallback_with_salary" };
    }

    // 不满足任何条件
    // 返回不符合推荐资格的结果
    return { isEligible: false, triggerType: null };
  }

  /**
   * 验证职级是否在对照表中存在
   */
  validateJobLevel(jobLevel) {
    if (!jobLevel) return false;

    // 使用现有的extractLevelFromMessage方法进行验证
    // 该方法已包含P级别、T级别、数字级别等完整的职级识别逻辑
    const extractedLevel = this.extractLevelFromMessage(jobLevel);

    // 如果能提取到有效职级，则认为职级有效
    // 支持P级别、T级别、数字级别、中文职级等多种格式
    return extractedLevel !== null;
  }

  /**
   * 检测第三方推荐请求
   */
  detectThirdPartyRequest(message) {
    if (!message) return { isThirdParty: false, targetRole: null };

    // 第三方推荐关键词
    const thirdPartyKeywords = [
      "给朋友",
      "给同事",
      "给家人",
      "为朋友",
      "为同事",
      "为家人",
      "朋友想看",
      "同事想看",
      "家人想看",
      "帮朋友",
      "帮同事",
      "帮家人",
      "朋友需要",
      "同事需要",
      "家人需要",
      "推荐给",
      "介绍给",
      "分享给",
    ];

    // 目标角色关键词
    const roleKeywords = {
      朋友: ["朋友", "好友", "兄弟", "姐妹"],
      同事: ["同事", "同学", "伙伴", "队友"],
      家人: ["家人", "亲人", "老婆", "老公", "妻子", "丈夫", "儿子", "女儿"],
    };

    // 检测第三方推荐
    const isThirdParty = thirdPartyKeywords.some((keyword) =>
      message.includes(keyword)
    );

    // 识别目标角色
    let targetRole = null;
    if (isThirdParty) {
      for (const [role, keywords] of Object.entries(roleKeywords)) {
        if (keywords.some((keyword) => message.includes(keyword))) {
          targetRole = role;
          break;
        }
      }
    }

    return { isThirdParty, targetRole };
  }

  /**
   * 检查精准推荐资格（第二次推荐条件）
   */
  checkPreciseRecommendationEligibility(infoState) {
    const { 所在城市, 业务场景 } = infoState;

    // 需要在基础推荐条件基础上，增加城市和业务场景
    const basicEligibility = this.checkRecommendationEligibility(infoState);
    return basicEligibility.isEligible && 所在城市 && 业务场景;
  }

  /**
   * 映射用户信息到数据库字段
   */
  mapInfoToProfile(infoUpdates) {
    const mapping = {
      所在公司: "current_company_name_raw",
      技术方向: "candidate_tech_direction_raw",
      当前职级: "candidate_level_raw",
      期望薪资: "expected_compensation_raw",
      所在城市: "desired_location_raw",
      业务场景: "candidate_business_scenario_raw",
    };

    const profileUpdates = {};
    Object.entries(infoUpdates).forEach(([key, value]) => {
      if (mapping[key]) {
        profileUpdates[mapping[key]] = value;
      }
    });

    return profileUpdates;
  }

  /**
   * 获取缺失的信息项
   */
  getMissingInfo(infoState) {
    const required = ["所在公司", "技术方向", "当前职级", "期望薪资"];
    const optional = ["所在城市", "业务场景"];

    const missingRequired = required.filter((key) => !infoState[key]);
    const missingOptional = optional.filter((key) => !infoState[key]);

    return {
      required: missingRequired,
      optional: missingOptional,
      nextToCollect: missingRequired[0] || missingOptional[0],
    };
  }

  /**
   * 检查管理器状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 信息提取模块 ====================

  /**
   * 从用户消息中提取信息
   */
  async extractUserInfo(message) {
    try {
      // 优先使用Qwen进行信息提取
      let qwenExtractedInfo = {};
      try {
        const qwenResult = await this.aiServices.extractInfoWithQwen(message, {
          extractionFields: [
            "技术方向",
            "所在公司",
            "当前职级",
            "期望薪资",
            "所在城市",
            "业务场景",
          ],
          messageContext: message,
        });

        if (qwenResult && Object.keys(qwenResult).length > 0) {
          qwenExtractedInfo = qwenResult;
          console.log("🤖 Qwen提取到信息:", qwenExtractedInfo);
        }
      } catch (qwenError) {
        console.warn("⚠️ Qwen信息提取失败，降级到正则提取:", qwenError.message);
      }

      // 使用正则表达式提取作为兜底
      const regexExtractedInfo = {};

      // 提取技术方向
      const techDirection = this.extractTechDirection(message);
      if (techDirection) {
        regexExtractedInfo.技术方向 = techDirection;
      }

      // 提取公司信息
      const company = this.extractCompanyFromMessage(message);
      if (company) {
        regexExtractedInfo.所在公司 = company;
      }

      // 提取职级信息
      const level = this.extractLevelFromMessage(message);
      if (level) {
        regexExtractedInfo.当前职级 = level;
      }

      // 提取薪资信息
      const salary = this.extractSalaryFromMessage(message);
      if (salary) {
        regexExtractedInfo.期望薪资 = salary;
      }

      // 提取城市信息
      const city = this.extractCityFromMessage(message);
      if (city) {
        regexExtractedInfo.所在城市 = city;
      }

      // 提取业务场景
      const businessScenario = this.extractBusinessScenario(message);
      if (businessScenario) {
        regexExtractedInfo.业务场景 = businessScenario;
      }

      // 合并AI提取和规则提取的结果，AI结果优先
      const finalExtractedInfo = {
        ...regexExtractedInfo,
        ...qwenExtractedInfo,
      };

      console.log("📝 最终解析到用户信息:", finalExtractedInfo);
      return finalExtractedInfo;
    } catch (error) {
      console.error("❌ 提取用户信息失败:", error);
      return {};
    }
  }

  /**
   * 提取技术方向
   */
  extractTechDirection(message) {
    const techKeywords = {
      推荐算法: ["推荐算法", "推荐系统", "个性化推荐", "协同过滤", "推荐引擎"],
      搜索算法: ["搜索算法", "搜索引擎", "信息检索", "搜索排序", "查询理解"],
      "CV算法（计算机视觉）": [
        "计算机视觉",
        "CV",
        "图像识别",
        "目标检测",
        "人脸识别",
        "图像处理",
      ],
      "NLP算法（自然语言处理）": [
        "自然语言处理",
        "NLP",
        "文本分析",
        "语言模型",
        "机器翻译",
        "文本挖掘",
      ],
      "大模型（LLM）算法": [
        "大模型",
        "LLM",
        "GPT",
        "BERT",
        "Transformer",
        "预训练模型",
      ],
      多模态算法: ["多模态", "视觉语言", "图文理解", "跨模态"],
      "通用机器学习/深度学习算法": [
        "机器学习",
        "深度学习",
        "神经网络",
        "算法工程师",
        "AI算法",
      ],
    };

    for (const [tech, keywords] of Object.entries(techKeywords)) {
      if (keywords.some((keyword) => message.includes(keyword))) {
        return tech;
      }
    }

    return null;
  }

  /**
   * 提取公司信息
   */
  extractCompanyFromMessage(message) {
    const companies = [
      "腾讯",
      "阿里巴巴",
      "阿里",
      "百度",
      "字节跳动",
      "美团",
      "京东",
      "滴滴",
      "小米",
      "华为",
      "OPPO",
      "vivo",
      "网易",
      "新浪",
      "搜狐",
      "360",
      "快手",
      "拼多多",
      "蚂蚁金服",
      "蚂蚁",
      "饿了么",
      "高德",
      "钉钉",
      "微软",
      "谷歌",
      "苹果",
      "Facebook",
      "亚马逊",
      "特斯拉",
    ];

    for (const company of companies) {
      if (message.includes(company)) {
        return company;
      }
    }

    // 检查是否有"在...工作"的模式
    const workPattern = /在(.{2,10}?)工作/;
    const workMatch = message.match(workPattern);
    if (workMatch) {
      return workMatch[1];
    }

    return null;
  }

  /**
   * 提取职级信息
   */
  extractLevelFromMessage(message) {
    // P级别
    const pLevelPattern = /P(\d+)/i;
    const pMatch = message.match(pLevelPattern);
    if (pMatch) {
      return `P${pMatch[1]}`;
    }

    // T级别
    const tLevelPattern = /T(\d+)/i;
    const tMatch = message.match(tLevelPattern);
    if (tMatch) {
      return `T${tMatch[1]}`;
    }

    // 数字级别（如"10级"）
    const numLevelPattern = /(\d+)级/;
    const numMatch = message.match(numLevelPattern);
    if (numMatch) {
      return `${numMatch[1]}级`;
    }

    // 职位级别关键词
    const levelKeywords = {
      初级: ["初级", "junior", "Junior"],
      中级: ["中级", "middle", "Middle"],
      高级: ["高级", "senior", "Senior"],
      专家: ["专家", "expert", "Expert"],
      资深: ["资深", "principal", "Principal"],
      架构师: ["架构师", "architect", "Architect"],
    };

    for (const [level, keywords] of Object.entries(levelKeywords)) {
      if (
        keywords.some((keyword) =>
          message.toLowerCase().includes(keyword.toLowerCase())
        )
      ) {
        return level;
      }
    }

    return null;
  }

  /**
   * 提取薪资信息
   */
  extractSalaryFromMessage(message) {
    // 匹配各种薪资格式
    const salaryPatterns = [
      /(\d+)万/,
      /(\d+)k/i,
      /(\d+)K/,
      /薪资(\d+)/,
      /期望(\d+)/,
      /(\d+)左右/,
    ];

    for (const pattern of salaryPatterns) {
      const match = message.match(pattern);
      if (match) {
        const amount = parseInt(match[1]);
        if (amount > 1000) {
          // 如果是K格式，转换为万
          return `${Math.round(amount / 10)}万`;
        } else {
          return `${amount}万`;
        }
      }
    }

    return null;
  }

  /**
   * 提取城市信息
   */
  extractCityFromMessage(message) {
    const cities = [
      "北京",
      "上海",
      "深圳",
      "广州",
      "杭州",
      "成都",
      "武汉",
      "西安",
      "南京",
      "苏州",
      "天津",
      "重庆",
      "青岛",
      "大连",
      "厦门",
      "长沙",
      "郑州",
      "济南",
      "沈阳",
      "合肥",
      "福州",
      "昆明",
      "石家庄",
      "太原",
    ];

    for (const city of cities) {
      if (message.includes(city)) {
        return city;
      }
    }

    return null;
  }

  /**
   * 提取业务场景
   */
  extractBusinessScenario(message) {
    const scenarios = {
      电商: ["电商", "购物", "商城", "零售"],
      社交: ["社交", "聊天", "社区", "朋友圈"],
      视频: ["视频", "直播", "短视频", "影音"],
      游戏: ["游戏", "娱乐", "竞技"],
      金融: ["金融", "支付", "理财", "银行"],
      出行: ["出行", "打车", "地图", "导航"],
      教育: ["教育", "学习", "培训", "课程"],
      医疗: ["医疗", "健康", "医院", "诊断"],
    };

    for (const [scenario, keywords] of Object.entries(scenarios)) {
      if (keywords.some((keyword) => message.includes(keyword))) {
        return scenario;
      }
    }

    return null;
  }

  // ==================== 意图识别模块 ====================

  /**
   * 初始化意图识别服务
   */
  initializeIntentRecognizer(aiServices, utilities, database) {
    this.aiServices = aiServices;
    this.utilities = utilities;
    this.database = database;

    // 意图类型定义
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      JOB_DETAIL_REQUEST: "job_detail_request",
      UNKNOWN: "unknown",
    };
  }

  /**
   * 主要意图分析入口
   */
  async analyzeUserIntent(message, session, isFirstUserMessage = null) {
    try {
      if (isFirstUserMessage) {
        // 第一句回复使用三层处理逻辑
        return await this.analyzeFirstUserMessage(message, session);
      }

      // 优先使用Qwen进行意图分析
      let qwenAnalysis = null;
      try {
        qwenAnalysis = await this.aiServices.analyzeIntentWithQwen(message, {
          sessionContext: session.current_interaction_context,
          messageHistory: await this.getRecentMessages(session.id, 5),
          userProfile: session.user_profile || {},
        });

        // 验证Qwen分析结果的有效性
        if (qwenAnalysis && qwenAnalysis.confidence > 0.7) {
          // 高置信度直接使用Qwen结果
          return {
            type: qwenAnalysis.type,
            confidence: qwenAnalysis.confidence,
            entities: qwenAnalysis.entities || {},
            context: qwenAnalysis.context || {},
            source: "qwen_analysis",
          };
        }
      } catch (qwenError) {
        console.warn("⚠️ Qwen意图分析失败，降级到规则引擎:", qwenError.message);
      }

      // 使用原有AI服务分析意图作为备选
      const aiAnalysis = await this.aiServices.analyzeUserIntent(message, {
        sessionContext: session.current_interaction_context,
        messageHistory: await this.getRecentMessages(session.id, 5),
      });

      // 结合规则引擎进行意图识别
      const ruleBasedIntent = this.identifyIntentByRules(message);

      // 合并分析结果，优先使用Qwen结果
      let finalIntent;
      if (qwenAnalysis && qwenAnalysis.confidence >= 0.8) {
        finalIntent = { ...qwenAnalysis, source: "qwen_high_confidence" };
      } else if (ruleBasedIntent.confidence >= 0.8) {
        finalIntent = { ...ruleBasedIntent, source: "rule_high_confidence" };
      } else {
        finalIntent = this.mergeIntentAnalysis(aiAnalysis, ruleBasedIntent);
        finalIntent.source = "merged_analysis";
      }

      return {
        type: finalIntent.type,
        confidence: finalIntent.confidence,
        entities: finalIntent.entities || {},
        context: finalIntent.context || {},
        source: finalIntent.source || "merged_analysis",
      };
    } catch (error) {
      console.error("❌ 意图分析失败:", error);

      // 回退到规则引擎
      return this.identifyIntentByRules(message);
    }
  }

  /**
   * 分析第一句用户消息（三层处理逻辑）
   */
  async analyzeFirstUserMessage(message, _session) {
    try {
      // 第一层：关键词快速匹配
      const quickMatch = this.checkQuickPatterns(message);
      if (quickMatch) {
        return {
          type: quickMatch.type,
          confidence: 0.9,
          source: "quick_pattern",
          needsFollowUp: true,
          extractedInfo: {},
          messageCategory: quickMatch.category,
        };
      }

      // 第二层：信息提取处理
      const extractedInfo = await this.extractUserInfo(message);
      if (extractedInfo.hasInfo) {
        return {
          type: this.intentTypes.PROFILE_UPDATE,
          confidence: 0.8,
          source: "info_extraction",
          needsFollowUp: true,
          extractedInfo: extractedInfo,
          messageCategory: "info_providing",
        };
      }

      // 第三层：AI意图分析
      const aiIntent = await this.aiServices.analyzeUserIntent(message, {
        isFirstMessage: true,
        context: "user_greeting_or_inquiry",
      });

      return {
        type: aiIntent.type || this.intentTypes.UNKNOWN,
        confidence: aiIntent.confidence || 0.5,
        source: "ai_analysis",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: aiIntent.category || "general_inquiry",
      };
    } catch (error) {
      console.error("❌ 第一句消息意图分析失败:", error);
      return {
        type: this.intentTypes.UNKNOWN,
        confidence: 0.3,
        source: "fallback",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: "unknown",
      };
    }
  }

  /**
   * 快速模式匹配（第一层）
   */
  checkQuickPatterns(message) {
    const lowerMessage = message.toLowerCase().trim();

    // 职位询问的精确匹配
    const jobInquiryPatterns = [
      "有什么职位",
      "有什么岗位",
      "有什么工作",
      "职位推荐",
      "岗位推荐",
      "工作推荐",
      "找工作",
      "求职",
    ];

    if (jobInquiryPatterns.some((pattern) => lowerMessage.includes(pattern))) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        category: "job_inquiry",
      };
    }

    // 问候语的精确匹配
    const greetingPatterns = ["你好", "hello", "嗨", "hi"];
    if (greetingPatterns.some((pattern) => lowerMessage.includes(pattern))) {
      return {
        type: this.intentTypes.GREETING,
        category: "greeting",
      };
    }

    return null;
  }

  /**
   * 基于规则的意图识别
   */
  identifyIntentByRules(message) {
    const lowerMessage = message.toLowerCase();

    // 职位详情查询识别（优先级最高）
    const detailMatch = lowerMessage.match(/详情(\d+)/);
    if (detailMatch) {
      return {
        type: this.intentTypes.JOB_DETAIL_REQUEST,
        confidence: 0.95,
        entities: {
          jobIndex: parseInt(detailMatch[1]),
        },
      };
    }

    // 问候语识别
    if (
      this.utilities.containsAny(lowerMessage, ["你好", "hello", "嗨", "开始"])
    ) {
      return {
        type: this.intentTypes.GREETING,
        confidence: 0.9,
        entities: {},
      };
    }

    // 职位搜索识别 - 优先级更高，包含职位相关的推荐
    if (
      this.utilities.containsAny(lowerMessage, [
        "找工作",
        "职位",
        "岗位",
        "招聘",
        "职位推荐",
        "岗位推荐",
        "工作推荐",
        "有什么职位",
        "有什么岗位",
        "有什么工作",
      ])
    ) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        confidence: 0.9,
        entities: {},
      };
    }

    // 推荐请求识别 - 非职位相关的推荐
    if (this.utilities.containsAny(lowerMessage, ["推荐", "建议", "合适的"])) {
      return {
        type: this.intentTypes.RECOMMENDATION_REQUEST,
        confidence: 0.7,
        entities: {},
      };
    }

    // 技术方向询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "技术",
        "开发",
        "前端",
        "后端",
        "算法",
      ])
    ) {
      return {
        type: this.intentTypes.TECH_DIRECTION_INQUIRY,
        confidence: 0.7,
        entities: {},
      };
    }

    // 薪资询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "薪资",
        "工资",
        "薪水",
        "待遇",
        "收入",
      ])
    ) {
      return {
        type: this.intentTypes.SALARY_INQUIRY,
        confidence: 0.8,
        entities: {},
      };
    }

    // 默认未知意图
    return {
      type: this.intentTypes.UNKNOWN,
      confidence: 0.3,
      entities: {},
    };
  }

  /**
   * 合并AI分析和规则分析的结果
   */
  mergeIntentAnalysis(aiAnalysis, ruleBasedIntent) {
    // 如果规则引擎有高置信度结果，优先使用
    if (ruleBasedIntent.confidence >= 0.8) {
      return ruleBasedIntent;
    }

    // 如果AI分析有高置信度结果，使用AI结果
    if (aiAnalysis.confidence >= 0.7) {
      return aiAnalysis;
    }

    // 否则选择置信度更高的
    return ruleBasedIntent.confidence >= aiAnalysis.confidence
      ? ruleBasedIntent
      : aiAnalysis;
  }

  /**
   * 获取最近的消息历史（用于AI分析上下文）
   */
  async getRecentMessages(sessionId, limit = 5) {
    if (!this.database) {
      return [];
    }

    try {
      return await this.database.getSessionMessages(sessionId, limit);
    } catch (error) {
      console.error("❌ 获取消息历史失败:", error);
      return [];
    }
  }
}

module.exports = UserManager;
