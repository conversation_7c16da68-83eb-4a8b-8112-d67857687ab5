/**
 * AI招聘助手系统 - AI服务模块
 *
 * 核心职责：
 * - DeepSeek API 集成
 * - 智能分析和生成
 * - 上下文理解
 * - 信息提取
 *
 * 预计代码量：1600行
 */

const axios = require("axios");

class AIServices {
  constructor(config) {
    this.config = config;
    this.client = null;
    this.isInitialized = false;

    // API配置
    this.apiEndpoint = config.deepseekEndpoint;
    this.apiKey = config.deepseekApiKey;
    this.maxTokens = config.maxTokens;
    this.temperature = config.temperature;
    this.timeout = config.timeout;

    // 高并发优化配置
    this.requestQueue = [];
    this.processing = false;
    this.maxRetries = 3;
    this.rateLimitDelay = 1000;
    this.maxConcurrentRequests = 5;
    this.currentRequests = 0;

    // AI调用限流机制配置
    this.rateLimiting = {
      maxQueueLength: 100,
      requestTimeout: 30000,
      rejectedRequests: 0,
      timeoutRequests: 0,
    };

    // 请求统计
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0,
    };

    // 错误处理和容错机制配置
    this.errorHandling = {
      apiTimeout: 30000, // 30秒超时
      maxFailures: 10,
      failureWindow: 60000, // 1分钟窗口
      adminNotificationThreshold: 5, // 连续失败5次后通知管理员
      consecutiveFailures: 0,
      lastFailureTime: null,
      failureLog: [],
    };

    // Qwen模型配置
    this.qwenConfig = {
      apiKey: config.qwenApiKey,
      endpoint: config.qwenEndpoint,
      model: config.qwenModel,
      timeout: config.timeout || 30000,
      maxRetries: 3,
    };

    // 双模型统计数据
    this.modelStats = {
      qwen: {
        requests: 0,
        failures: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        successRate: 0,
      },
      deepseek: {
        requests: 0,
        failures: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        successRate: 0,
      },
    };

    // 模型角色分工配置
    this.modelRoles = {
      qwen: {
        primary: ["intent_analysis", "info_extraction", "user_analysis"],
        description: "Qwen负责意图识别、信息提取、用户分析",
      },
      deepseek: {
        primary: ["response_generation", "conversation_inference"],
        description: "DeepSeek负责对话生成、回复推理",
      },
    };

    // 模型调用队列和并发控制
    this.requestQueues = {
      qwen: [],
      deepseek: [],
    };
    this.concurrencyLimits = {
      qwen: 3,
      deepseek: 3,
      total: 5,
    };
    this.activeRequests = {
      qwen: 0,
      deepseek: 0,
      total: 0,
    };
  }

  /**
   * 初始化AI服务
   */
  async initialize() {
    try {
      // 配置HTTP客户端（使用3秒超时）
      this.client = axios.create({
        baseURL: this.apiEndpoint,
        timeout: this.errorHandling.apiTimeout,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // 在测试环境下跳过连接测试
      if (this.apiKey && this.apiKey !== "test-key") {
        const connectionSuccess = await this.testConnection();
        if (!connectionSuccess) {
          console.warn("⚠️ AI服务连接失败，系统将在离线模式下运行");
        }
      } else {
        console.log("🧪 测试环境：跳过AI服务连接测试");
      }

      this.isInitialized = true;
      console.log("🤖 AI服务初始化完成");
    } catch (error) {
      console.error("❌ AI服务初始化失败:", error);
      // 在测试环境下不抛出错误
      if (this.apiKey === "test-key") {
        console.log("🧪 测试环境：忽略初始化错误");
        this.isInitialized = true;
      } else {
        throw error;
      }
    }
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    try {
      const response = await this.client.post("/chat/completions", {
        model: "deepseek-chat",
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 10,
      });

      if (response.status === 200) {
        console.log("✅ AI服务连接测试成功");
        return true;
      }
    } catch (error) {
      console.error("❌ AI服务连接测试失败:", error);
      console.warn("⚠️ 系统将在AI服务离线模式下继续运行");
      this.isOfflineMode = true;
      return false;
    }
  }

  /**
   * 分析用户意图（带队列处理）
   */
  async analyzeUserIntent(message, context = {}) {
    return this.queueRequest(
      async () => {
        const prompt = this.buildIntentAnalysisPrompt(message, context);

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: [
            { role: "system", content: this.getIntentAnalysisSystemPrompt() },
            { role: "user", content: prompt },
          ],
          max_tokens: 500,
          temperature: 0.3,
        });

        const result = response.data.choices[0].message.content;
        return this.parseIntentAnalysisResult(result);
      },
      {
        fallback: {
          type: "unknown",
          confidence: 0.1,
          entities: {},
        },
      }
    );
  }

  /**
   * 请求队列处理（带限流机制）
   */
  async queueRequest(requestFn, options = {}) {
    return new Promise((resolve, reject) => {
      // 检查队列长度限制
      if (this.requestQueue.length >= this.rateLimiting.maxQueueLength) {
        this.rateLimiting.rejectedRequests++;
        console.warn(
          `⚠️ 队列已满，拒绝请求。当前队列长度: ${this.requestQueue.length}`
        );
        reject(new Error("QUEUE_FULL"));
        return;
      }

      const request = {
        requestFn,
        resolve,
        reject,
        options,
        retries: 0,
        timestamp: Date.now(),
        timeoutId: setTimeout(
          () => this.handleRequestTimeout(request),
          this.rateLimiting.requestTimeout
        ),
      };

      this.requestQueue.push(request);
      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) {
      return;
    }

    if (this.currentRequests >= this.maxConcurrentRequests) {
      return;
    }

    this.processing = true;
    const request = this.requestQueue.shift();
    this.currentRequests++;

    // 清除请求超时定时器
    if (request.timeoutId) {
      clearTimeout(request.timeoutId);
      request.timeoutId = null;
    }

    try {
      const startTime = Date.now();
      console.log(
        `🔄 [${new Date().toISOString()}] 开始执行队列请求，当前并发数: ${this.currentRequests}`
      );

      const result = await request.requestFn();

      console.log(
        `✅ [${new Date().toISOString()}] 队列请求执行成功，耗时: ${Date.now() - startTime}ms`
      );

      // 更新统计信息
      this.updateStats(true, Date.now() - startTime);

      request.resolve(result);
    } catch (error) {
      console.error("❌ AI请求失败:", error);

      // 检查是否需要重试
      if (request.retries < this.maxRetries && this.shouldRetry(error)) {
        request.retries++;
        this.requestQueue.unshift(request); // 重新加入队列头部

        // 如果是限流错误，延迟处理
        if (this.isRateLimitError(error)) {
          this.stats.rateLimitHits++;
          await this.delay(this.rateLimitDelay);
        }
      } else {
        // 重试次数用完或不可重试错误，返回降级结果
        this.updateStats(false, 0);

        if (request.options.fallback) {
          request.resolve(request.options.fallback);
        } else {
          request.reject(error);
        }
      }
    } finally {
      this.currentRequests--;
      this.processing = false;

      // 继续处理队列
      setTimeout(() => this.processQueue(), 10);
    }
  }

  /**
   * 发起API请求（带重试和限流处理）
   */
  async makeAPIRequest(endpoint, data) {
    const requestId = Math.random().toString(36).substr(2, 9);
    const startTime = Date.now();

    try {
      console.log(
        `🚀 [${new Date().toISOString()}] [${requestId}] 开始API请求: ${endpoint}`
      );
      console.log(
        `📤 [${new Date().toISOString()}] [${requestId}] 请求数据:`,
        JSON.stringify(data, null, 2)
      );
      console.log(
        `🔗 [${new Date().toISOString()}] [${requestId}] 请求URL: ${this.client.defaults.baseURL}${endpoint}`
      );
      console.log(
        `⏰ [${new Date().toISOString()}] [${requestId}] 超时设置: ${this.client.defaults.timeout}ms`
      );

      const response = await this.client.post(endpoint, data);

      const responseTime = Date.now() - startTime;
      console.log(
        `✅ [${new Date().toISOString()}] [${requestId}] API请求成功，耗时: ${responseTime}ms`
      );
      console.log(
        `📥 [${new Date().toISOString()}] [${requestId}] 响应状态: ${response.status}`
      );
      console.log(
        `📥 [${new Date().toISOString()}] [${requestId}] 响应数据:`,
        JSON.stringify(response.data, null, 2)
      );

      this.handleAPISuccess();
      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(
        `❌ [${new Date().toISOString()}] [${requestId}] API请求失败，耗时: ${responseTime}ms`
      );
      console.error(
        `💥 [${new Date().toISOString()}] [${requestId}] 错误类型: ${error.name}`
      );
      console.error(
        `💥 [${new Date().toISOString()}] [${requestId}] 错误消息: ${error.message}`
      );
      console.error(
        `💥 [${new Date().toISOString()}] [${requestId}] 错误代码: ${error.code}`
      );

      if (error.response) {
        console.error(
          `💥 [${new Date().toISOString()}] [${requestId}] HTTP状态: ${error.response.status}`
        );
        console.error(
          `💥 [${new Date().toISOString()}] [${requestId}] 响应数据:`,
          JSON.stringify(error.response.data, null, 2)
        );
      }

      console.error(
        `💥 [${new Date().toISOString()}] [${requestId}] 完整错误栈:`,
        error.stack
      );

      this.handleAPIFailure(error, endpoint, data);

      // 处理特定错误类型
      if (error.response?.status === 429) {
        // 限流错误
        throw new Error("RATE_LIMIT");
      } else if (error.response?.status >= 500) {
        // 服务器错误，可重试
        throw new Error("SERVER_ERROR");
      } else {
        // 客户端错误，不重试
        throw error;
      }
    }
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = ["RATE_LIMIT", "SERVER_ERROR", "TIMEOUT"];
    return (
      retryableErrors.includes(error.message) ||
      error.code === "ECONNRESET" ||
      error.code === "ETIMEDOUT"
    );
  }

  /**
   * 判断是否是限流错误
   */
  isRateLimitError(error) {
    return error.message === "RATE_LIMIT" || error.response?.status === 429;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 处理请求超时
   */
  handleRequestTimeout(request) {
    const index = this.requestQueue.indexOf(request);
    if (index > -1) {
      this.requestQueue.splice(index, 1);
      this.rateLimiting.timeoutRequests++;
      console.warn(
        `⏰ 请求超时，已从队列移除。等待时间: ${Date.now() - request.timestamp}ms`
      );
      request.reject(new Error("REQUEST_TIMEOUT"));
    }
  }

  /**
   * 更新统计信息
   */
  updateStats(success, responseTime) {
    this.stats.totalRequests++;

    if (success) {
      this.stats.successfulRequests++;

      // 计算平均响应时间
      const totalTime =
        this.stats.averageResponseTime * (this.stats.successfulRequests - 1) +
        responseTime;
      this.stats.averageResponseTime =
        totalTime / this.stats.successfulRequests;
    } else {
      this.stats.failedRequests++;
    }
  }

  /**
   * 处理API调用成功
   */
  handleAPISuccess() {
    this.errorHandling.consecutiveFailures = 0;
    this.errorHandling.lastFailureTime = null;
  }

  /**
   * 处理API调用失败
   */
  handleAPIFailure(error, endpoint, data) {
    const now = Date.now();
    this.errorHandling.consecutiveFailures++;
    this.errorHandling.lastFailureTime = now;

    // 记录详细失败日志
    const failureRecord = {
      timestamp: new Date().toISOString(),
      endpoint: endpoint,
      error: error.message,
      status: error.response?.status || "UNKNOWN",
      consecutiveFailures: this.errorHandling.consecutiveFailures,
    };

    this.errorHandling.failureLog.push(failureRecord);

    // 保持失败日志在合理大小
    if (this.errorHandling.failureLog.length > 50) {
      this.errorHandling.failureLog = this.errorHandling.failureLog.slice(-25);
    }

    // 检查是否需要发送管理员通知
    if (
      this.errorHandling.consecutiveFailures >=
      this.errorHandling.adminNotificationThreshold
    ) {
      this.sendAdminNotification(failureRecord);
    }

    console.error(`❌ API调用失败 [${endpoint}]:`, {
      error: error.message,
      status: error.response?.status,
      consecutiveFailures: this.errorHandling.consecutiveFailures,
      timestamp: failureRecord.timestamp,
    });
  }

  /**
   * 发送管理员通知
   */
  sendAdminNotification(failureRecord) {
    const successRate = this.getSuccessRate();

    console.log("\n🚨 ===== 管理员通知 =====");
    console.log(
      `⚠️  AI服务连续失败 ${this.errorHandling.consecutiveFailures} 次`
    );
    console.log(`📊 当前成功率: ${successRate}%`);
    console.log(`🕐 最后失败时间: ${failureRecord.timestamp}`);
    console.log(`🔗 失败端点: ${failureRecord.endpoint}`);
    console.log(`💥 错误信息: ${failureRecord.error}`);
    console.log(`📈 总请求数: ${this.stats.totalRequests}`);
    console.log(`❌ 总失败数: ${this.stats.failedRequests}`);
    console.log("========================\n");
  }

  /**
   * 获取成功率
   */
  getSuccessRate() {
    if (this.stats.totalRequests === 0) return 100;
    return (
      (this.stats.successfulRequests / this.stats.totalRequests) *
      100
    ).toFixed(2);
  }

  /**
   * 构建意图分析提示词
   */
  buildIntentAnalysisPrompt(message, context) {
    let prompt = `你是一个专业的招聘助手意图分析器。请分析用户消息的意图类型。

用户消息: "${message}"

可能的意图类型包括：
- job_search: 搜索职位、找工作
- greeting: 问候语、打招呼
- profile_update: 更新个人档案、提供个人信息
- recommendation_request: 请求推荐
- tech_direction_inquiry: 技术方向询问
- salary_inquiry: 薪资询问
- company_inquiry: 公司询问
- unknown: 未知意图

请严格按照以下JSON格式返回分析结果，不要添加任何其他内容：
{
  "type": "意图类型",
  "confidence": 0.9,
  "entities": {},
  "context": {}
}`;

    if (context.sessionContext) {
      prompt += `\n\n会话上下文: ${JSON.stringify(context.sessionContext)}`;
    }

    if (context.messageHistory && context.messageHistory.length > 0) {
      prompt += `\n\n最近对话历史:\n`;
      context.messageHistory.forEach((msg, index) => {
        prompt += `${msg.message_type}: ${msg.message_content}\n`;
      });
    }

    return prompt;
  }

  /**
   * 🛠 FIXED-PROBLEM-5: 修复响应质量问题，优化意图分析系统提示词 + 2025-07-31
   * 获取意图分析系统提示词
   */
  getIntentAnalysisSystemPrompt() {
    return `你是一个专业的招聘助手意图分析器。请准确分析用户消息的意图类型。

重要规则：
1. 当用户提供个人信息（如"我是Java工程师"、"有3年经验"、"期望薪资30k"）时，应识别为profile_update
2. 当用户询问职位或工作时，应识别为job_search
3. 当用户问候时，应识别为greeting
4. 尽量避免返回unknown，优先匹配最相近的意图类型

可能的意图类型包括：
- greeting: 问候语、打招呼
- profile_update: 更新个人档案、提供个人信息（技术栈、经验、薪资、地点等）
- job_search: 搜索职位、找工作、职位推荐
- recommendation_request: 请求推荐
- tech_direction_inquiry: 技术方向询问
- salary_inquiry: 薪资询问
- company_inquiry: 公司询问
- location_inquiry: 地点询问
- experience_inquiry: 经验询问
- resume_upload: 简历上传
- unknown: 未知意图（仅在完全无法理解时使用）

请以JSON格式返回分析结果：
{
  "type": "意图类型",
  "confidence": 0.0-1.0的置信度,
  "entities": {
    "技术栈": ["提取的技术"],
    "经验年限": "提取的年限",
    "期望薪资": "提取的薪资",
    "工作地点": "提取的地点"
  },
  "context": {
    "需要澄清": true/false,
    "澄清问题": "需要澄清的具体问题"
  }
}`;
  }

  /**
   * 解析意图分析结果
   */
  parseIntentAnalysisResult(result) {
    try {
      // 清理结果，移除markdown代码块标记
      let cleanResult = result.trim();
      if (cleanResult.startsWith("```json")) {
        cleanResult = cleanResult
          .replace(/^```json\s*/, "")
          .replace(/\s*```$/, "");
      } else if (cleanResult.startsWith("```")) {
        cleanResult = cleanResult.replace(/^```\s*/, "").replace(/\s*```$/, "");
      }

      // 修复常见的JSON格式问题
      cleanResult = cleanResult
        .replace(/,(\s*[}\]])/g, "$1") // 移除对象和数组末尾的多余逗号
        .replace(/'/g, '"'); // 将单引号替换为双引号

      // 尝试解析JSON
      const parsed = JSON.parse(cleanResult);

      // 🛠 FIXED-PROBLEM-2: 修复意图识别不一致问题，规范化意图类型 + 2025-07-31
      const normalizedType = this.normalizeIntentType(parsed.type || "unknown");

      return {
        type: normalizedType,
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || {},
        context: parsed.context || {},
      };
    } catch (error) {
      console.error("❌ 解析意图分析结果失败:", error);

      // 回退到简单解析
      return {
        type: "unknown",
        confidence: 0.3,
        entities: {},
      };
    }
  }

  /**
   * 🛠 FIXED-PROBLEM-2: 添加意图类型规范化方法 + 2025-07-31
   * 规范化意图类型，确保一致性
   */
  normalizeIntentType(type) {
    if (!type) return "unknown";

    const originalType = type.toString();
    const typeStr = originalType.toLowerCase();

    // 🛠 FIXED-PROBLEM-2: 修复意图类型映射，同时支持原始和小写查找 + 2025-07-31
    // 意图类型映射表
    const intentMapping = {
      // 小写版本
      job_search: "job_search",
      jobsearch: "job_search",
      greeting: "greeting",
      profile_update: "profile_update",
      profileupdate: "profile_update",
      recommendation_request: "recommendation_request",
      recommendationrequest: "recommendation_request",
      tech_direction_inquiry: "tech_direction_inquiry",
      techdirectioninquiry: "tech_direction_inquiry",
      salary_inquiry: "salary_inquiry",
      salaryinquiry: "salary_inquiry",
      company_inquiry: "company_inquiry",
      companyinquiry: "company_inquiry",
      unknown: "unknown",
      // 大写版本
      JOB_SEARCH: "job_search",
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      UNKNOWN: "unknown",
    };

    // 先尝试原始类型，再尝试小写类型
    return intentMapping[originalType] || intentMapping[typeStr] || "unknown";
  }

  /**
   * 使用DeepSeek生成对话回复（带队列处理）
   */
  async generateResponseWithDeepSeek(prompt, context = {}) {
    this.modelStats.deepseek.requests++;
    const startTime = Date.now();

    return this.queueRequest(
      async () => {
        const messages = [
          { role: "system", content: this.getConversationSystemPrompt() },
          { role: "user", content: prompt },
        ];

        if (context.messageHistory) {
          const historyMessages = context.messageHistory
            .slice(-5)
            .map((msg) => ({
              role: msg.message_type === "user" ? "user" : "assistant",
              content: msg.message_content,
            }));
          messages.splice(1, 0, ...historyMessages);
        }

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: messages,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
        });

        const responseTime = Date.now() - startTime;
        this.modelStats.deepseek.totalResponseTime += responseTime;
        this.modelStats.deepseek.averageResponseTime =
          this.modelStats.deepseek.totalResponseTime /
          this.modelStats.deepseek.requests;
        this.modelStats.deepseek.successRate = (
          ((this.modelStats.deepseek.requests -
            this.modelStats.deepseek.failures) /
            this.modelStats.deepseek.requests) *
          100
        ).toFixed(2);

        return response.data.choices[0].message.content;
      },
      {
        fallback: (() => {
          this.modelStats.deepseek.failures++;
          this.modelStats.deepseek.successRate = (
            ((this.modelStats.deepseek.requests -
              this.modelStats.deepseek.failures) /
              this.modelStats.deepseek.requests) *
            100
          ).toFixed(2);
          return "抱歉，我现在无法生成回复。请稍后再试。";
        })(),
      }
    );
  }

  /**
   * 🛠 FIXED-PROBLEM-5: 优化对话系统提示词，提升响应质量 + 2025-07-31
   * 获取对话系统提示词
   */
  getConversationSystemPrompt() {
    return `你是Katrina，一个专业的AI招聘助手。

【核心能力】：
1. 理解用户的技术背景和求职需求
2. 收集关键信息：技术栈、工作经验、期望薪资、工作地点等
3. 提供专业的职业建议和引导
4. 确认和澄清用户提供的信息

【智能理解原则】：
- 当用户说"我是Java工程师"时，理解为技术栈信息
- 当用户说"有3年经验"时，理解为工作经验信息
- 当用户说"期望薪资30k"时，理解为薪资期望信息
- 当用户说"想在北京工作"时，理解为地点偏好信息
- 对用户提供的信息要给予积极确认和回应

【回复策略】：
- 对用户提供的信息表示理解和确认
- 基于已有信息，询问缺失的关键信息
- 提供个性化的建议和引导
- 避免返回"不理解"的通用回复

【严格限制】：
1. 不能虚构具体的职位信息
2. 不能编造公司名称或招聘信息
3. 不能给出具体的薪资数字

【回复风格】：
- 专业、友好、有针对性
- 体现对用户技术背景的理解
- 每次回复重点突出1-2个关键问题
- 简洁明了，避免冗长

记住：要展现出对用户技术背景的理解，而不是简单地说"不理解"！`;
  }

  /**
   * 检查服务状态
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      initialized: this.isInitialized,
      endpoint: this.apiEndpoint,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      queue: {
        pending: this.requestQueue.length,
        processing: this.currentRequests,
        maxConcurrent: this.maxConcurrentRequests,
      },
      performance: {
        ...this.stats,
        successRate:
          this.stats.totalRequests > 0
            ? (
                (this.stats.successfulRequests / this.stats.totalRequests) *
                100
              ).toFixed(2) + "%"
            : "0%",
      },
    };
  }

  /**
   * 获取队列状态（增强版）
   */
  getQueueStatus() {
    const avgWaitTime =
      this.requestQueue.length > 0
        ? this.requestQueue.reduce(
            (sum, req) => sum + (Date.now() - req.timestamp),
            0
          ) / this.requestQueue.length
        : 0;
    return {
      pending: this.requestQueue.length,
      processing: this.currentRequests,
      maxConcurrent: this.maxConcurrentRequests,
      maxQueueLength: this.rateLimiting.maxQueueLength,
      queueUtilization:
        (
          (this.requestQueue.length / this.rateLimiting.maxQueueLength) *
          100
        ).toFixed(2) + "%",
      averageWaitTime: Math.round(avgWaitTime),
      rejectedRequests: this.rateLimiting.rejectedRequests,
      timeoutRequests: this.rateLimiting.timeoutRequests,
      isHealthy:
        this.requestQueue.length < this.rateLimiting.maxQueueLength * 0.8 &&
        this.currentRequests < this.maxConcurrentRequests,
    };
  }

  /**
   * 使用Qwen进行意图分析
   */
  async analyzeIntentWithQwen(message, context) {
    try {
      this.modelStats.qwen.requests++;
      const startTime = Date.now();
      const prompt = this.buildIntentAnalysisPrompt(message, context);
      const response = await this.callQwenAPI(prompt, {
        max_tokens: 200,
        temperature: 0.1,
      });

      const responseTime = Date.now() - startTime;
      this.modelStats.qwen.totalResponseTime += responseTime;
      this.modelStats.qwen.averageResponseTime =
        this.modelStats.qwen.totalResponseTime / this.modelStats.qwen.requests;

      if (!response) {
        this.modelStats.qwen.failures++;
        return null;
      }

      const intentResult = this.parseIntentAnalysisResult(response);
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return intentResult;
    } catch (error) {
      console.error("❌ Qwen意图分析失败:", error.message);
      this.modelStats.qwen.failures++;
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return null;
    }
  }

  /**
   * 使用Qwen进行信息提取
   */
  async extractInfoWithQwen(message, userProfile) {
    try {
      this.modelStats.qwen.requests++;
      const startTime = Date.now();
      const prompt = this.buildInfoExtractionPrompt(message, userProfile);
      const response = await this.callQwenAPI(prompt, {
        max_tokens: 300,
        temperature: 0.1,
      });

      const responseTime = Date.now() - startTime;
      this.modelStats.qwen.totalResponseTime += responseTime;
      this.modelStats.qwen.averageResponseTime =
        this.modelStats.qwen.totalResponseTime / this.modelStats.qwen.requests;

      if (!response) {
        this.modelStats.qwen.failures++;
        return null;
      }

      const extractedInfo = this.parseInfoExtractionResult(response);
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return extractedInfo;
    } catch (error) {
      console.error("❌ Qwen信息提取失败:", error.message);
      this.modelStats.qwen.failures++;
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return null;
    }
  }

  // ==================== 缺失的核心方法 ====================

  /**
   * 调用Qwen API（带容错机制）
   */
  async callQwenAPI(prompt, options = {}) {
    try {
      console.log("🤖 调用Qwen API:", prompt.substring(0, 100) + "...");

      if (!this.qwenConfig.apiKey) {
        this.handleAPIFailure(new Error("Qwen API密钥未配置"), "qwen-api", {});
        return null;
      }

      const requestBody = {
        model: this.qwenConfig.model,
        input: {
          messages: [
            {
              role: "user",
              content: prompt,
            },
          ],
        },
        parameters: {
          max_tokens: options.max_tokens || 500,
          temperature: options.temperature || 0.7,
          top_p: 0.8,
        },
      };

      const response = await axios.post(this.qwenConfig.endpoint, requestBody, {
        headers: {
          Authorization: `Bearer ${this.qwenConfig.apiKey}`,
          "Content-Type": "application/json",
        },
        timeout: this.errorHandling.apiTimeout, // 使用3秒超时
      });

      // Qwen API的响应格式是 response.data.output.text
      if (response.data && response.data.output && response.data.output.text) {
        const content = response.data.output.text;
        console.log("✅ Qwen API调用成功，返回内容:", content);
        this.handleAPISuccess();
        return content;
      } else {
        console.error("❌ Qwen API响应格式异常，实际响应:", response.data);
        this.handleAPIFailure(
          new Error("Qwen API响应格式异常"),
          "qwen-api",
          requestBody
        );
        return null;
      }
    } catch (error) {
      console.error("❌ Qwen API调用失败:", error.message);
      this.handleAPIFailure(error, "qwen-api", {
        prompt: prompt.substring(0, 100),
      });
      return null; // 返回null而不是抛出异常
    }
  }

  /**
   * 构建信息提取提示词
   */
  buildInfoExtractionPrompt(message, userProfile) {
    const extractionFields = userProfile.extractionFields || [
      "技术方向",
      "所在公司",
      "当前职级",
      "期望薪资",
      "所在城市",
      "业务场景",
    ];

    return `请从以下用户消息中提取信息：

用户消息: "${message}"

需要提取的字段: ${extractionFields.join(", ")}

【重要规则】：
1. 只提取消息中明确提到的信息
2. 如果消息与工作、技术、职业无关（如天气、问候等），返回空对象 {}
3. 不要推测或补充任何信息
4. 如果某个字段没有找到，请忽略该字段

请以JSON格式返回提取到的信息：

示例1（有信息）:
{
  "技术方向": "后端开发",
  "所在公司": "阿里巴巴",
  "当前职级": "P6"
}

示例2（无相关信息）:
{}`;
  }

  /**
   * 解析信息提取结果
   */
  parseInfoExtractionResult(response) {
    try {
      // 尝试解析JSON响应
      const parsed = JSON.parse(response);
      return parsed;
    } catch (error) {
      console.warn("⚠️ 解析信息提取结果失败，使用正则提取:", error.message);

      // 回退到正则表达式提取
      const extracted = {};

      // 提取技术方向
      const techMatch = response.match(
        /技术方向[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (techMatch) extracted.技术方向 = techMatch[1].trim();

      // 提取公司
      const companyMatch = response.match(
        /所在公司[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (companyMatch) extracted.所在公司 = companyMatch[1].trim();

      // 提取职级
      const levelMatch = response.match(
        /当前职级[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (levelMatch) extracted.当前职级 = levelMatch[1].trim();

      return extracted;
    }
  }
}

module.exports = AIServices;
