const TechMapper = require("./core/业务服务/tech-mapper.js");

console.log("✅ 技术映射器真实功能验证\n");

// 创建模拟数据库
const mockDatabase = {
  async getTechTree() {
    return [
      { id: 1, tech_name: "JavaScript", level: 1, category: "frontend" },
      { id: 2, tech_name: "React", level: 2, category: "frontend" },
      { id: 3, tech_name: "Vue.js", level: 2, category: "frontend" },
      { id: 4, tech_name: "Python", level: 1, category: "backend" },
      { id: 5, tech_name: "Django", level: 2, category: "backend" },
      { id: 6, tech_name: "Machine Learning", level: 1, category: "ai" },
      { id: 7, tech_name: "Deep Learning", level: 2, category: "ai" },
      { id: 8, tech_name: "Java", level: 1, category: "backend" },
      { id: 9, tech_name: "Spring", level: 2, category: "backend" },
      { id: 10, tech_name: "AI", level: 1, category: "ai" },
    ];
  },
};

async function testTechMapper() {
  try {
    const techMapper = new TechMapper(mockDatabase, {});

    // 正确初始化
    await techMapper.initialize();

    console.log("✅ 核心功能验证:");
    console.log(
      "✅ 同义词映射增强:",
      techMapper.mappingRules.synonyms.javascript.length >= 5
    );
    console.log(
      "✅ 新增同义词数量:",
      Object.keys(techMapper.mappingRules.synonyms).length > 10
    );

    // 测试技术映射功能
    console.log("\n🔍 技术映射测试:");
    const testCases = [
      "javascript",
      "js",
      "react",
      "python",
      "machine learning",
    ];

    for (const testCase of testCases) {
      const result = await techMapper.mapTechDirection(testCase);
      console.log(
        `📝 "${testCase}" -> ${result.success ? "✅ 成功映射到: " + result.matched.tech_name : "❌ 映射失败"}`
      );
    }

    console.log("\n✅ 技术映射器验证完成");
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    console.error("详细错误:", error);
    process.exit(1);
  }
}

testTechMapper();
