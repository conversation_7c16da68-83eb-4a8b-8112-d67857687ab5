/**
 * 测试阶段9和阶段10的修复
 */

require('dotenv').config({ path: '../.env' });
const AIServices = require('./core/数据管理/ai-services.js');
const UserManager = require('./core/业务服务/user-manager.js');

console.log('🔧 测试阶段9和阶段10的修复\n');

async function testFixes() {
  try {
    // 初始化AI服务
    console.log('📋 初始化AI服务...');
    const aiConfig = {
      deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT || 'https://api.deepseek.com/v1',
      deepseekApiKey: process.env.DEEPSEEK_API_KEY,
      qwenApiKey: process.env.QWEN_API_KEY,
      qwenEndpoint: process.env.QWEN_ENDPOINT,
      qwenModel: process.env.QWEN_MODEL || 'qwen-turbo',
      maxTokens: 1000,
      temperature: 0.7,
      timeout: 30000
    };
    
    const aiServices = new AIServices(aiConfig);
    await aiServices.initialize();
    console.log('✅ AI服务初始化完成');
    
    // 初始化用户管理器
    const userManager = new UserManager();
    console.log('✅ 用户管理器初始化完成\n');

    // 测试阶段9修复：空信息处理
    console.log('📋 测试阶段9修复：空信息处理');
    const emptyMessage = "你好，今天天气不错";
    console.log(`输入: "${emptyMessage}"`);
    
    try {
      const qwenResult = await aiServices.extractInfoWithQwen(emptyMessage, {
        extractionFields: ["技术方向", "所在公司", "当前职级", "期望薪资", "所在城市", "业务场景"]
      });
      console.log('Qwen提取结果:', qwenResult);
      
      if (!qwenResult || Object.keys(qwenResult).length === 0) {
        console.log('✅ 阶段9修复成功：正确识别为空信息');
      } else {
        console.log('❌ 阶段9修复失败：仍然提取了不存在的信息');
      }
    } catch (error) {
      console.log('⚠️ Qwen调用失败，这是正常的降级行为');
    }
    console.log('');

    // 测试阶段10修复：第三方推荐检测
    console.log('📋 测试阶段10修复：第三方推荐检测');
    const thirdPartyMessage = "我想为家人找份工作";
    console.log(`输入: "${thirdPartyMessage}"`);
    
    const detection = userManager.detectThirdPartyRequest(thirdPartyMessage);
    console.log('检测结果:', detection);
    
    if (detection.isThirdParty && detection.targetRole === '家人') {
      console.log('✅ 阶段10修复成功：正确检测到第三方推荐请求');
    } else {
      console.log('❌ 阶段10修复失败：未能检测到第三方推荐请求');
    }
    console.log('');

    // 额外测试几个第三方推荐案例
    console.log('📋 额外测试第三方推荐检测');
    const testCases = [
      "为朋友找工作",
      "为同事推荐职位", 
      "给家人看看有什么岗位"
    ];
    
    for (const testCase of testCases) {
      console.log(`输入: "${testCase}"`);
      const result = userManager.detectThirdPartyRequest(testCase);
      console.log('检测结果:', result);
      
      if (result.isThirdParty) {
        console.log('✅ 正确检测到第三方推荐请求');
      } else {
        console.log('❌ 未能检测到第三方推荐请求');
      }
      console.log('');
    }

    console.log('🎯 修复测试完成');

  } catch (error) {
    console.error('❌ 修复测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

testFixes().catch(console.error);
