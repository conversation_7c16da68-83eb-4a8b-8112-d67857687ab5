/**
 * AI服务调试测试
 */

require("dotenv").config({ path: "../.env" });
const AIServices = require("./core/数据管理/ai-services.js");

// 使用真实的环境变量配置
const realConfig = {
  deepseekEndpoint:
    process.env.DEEPSEEK_ENDPOINT || "https://api.deepseek.com/v1",
  deepseekApiKey: process.env.DEEPSEEK_API_KEY,
  qwenApiKey: process.env.QWEN_API_KEY,
  qwenEndpoint: process.env.QWEN_ENDPOINT,
  qwenModel: process.env.QWEN_MODEL || "qwen-turbo",
  maxTokens: 1000,
  temperature: 0.7,
  timeout: 30000,
};

console.log("🔑 使用的API密钥:");
console.log(
  "DeepSeek API Key:",
  realConfig.deepseekApiKey
    ? realConfig.deepseekApiKey.substring(0, 10) + "..."
    : "未设置"
);
console.log(
  "Qwen API Key:",
  realConfig.qwenApiKey
    ? realConfig.qwenApiKey.substring(0, 10) + "..."
    : "未设置"
);
console.log("");

console.log("🔍 AI服务调试测试开始\n");

async function testAIServices() {
  try {
    // 创建AI服务实例
    const aiServices = new AIServices(realConfig);

    // 初始化AI服务
    console.log("📋 测试0：初始化AI服务");
    await aiServices.initialize();
    console.log("✅ AI服务初始化成功\n");

    console.log("📋 测试1：检查AI服务方法是否存在");
    console.log(
      "analyzeUserIntent方法存在:",
      typeof aiServices.analyzeUserIntent === "function"
    );
    console.log(
      "analyzeIntentWithQwen方法存在:",
      typeof aiServices.analyzeIntentWithQwen === "function"
    );
    console.log(
      "extractInfoWithQwen方法存在:",
      typeof aiServices.extractInfoWithQwen === "function"
    );
    console.log("");

    console.log("📋 测试2：模拟调用analyzeIntentWithQwen");
    try {
      const qwenResult = await aiServices.analyzeIntentWithQwen("我想找工作", {
        sessionContext: {},
        messageHistory: [],
        userProfile: {},
      });
      console.log("Qwen意图分析结果:", qwenResult);
    } catch (error) {
      console.log("Qwen意图分析失败:", error.message);
    }
    console.log("");

    console.log("📋 测试3：模拟调用extractInfoWithQwen");
    try {
      const extractResult = await aiServices.extractInfoWithQwen(
        "我在阿里巴巴工作，职级是P6",
        {
          extractionFields: ["技术方向", "所在公司", "当前职级"],
          messageContext: "我在阿里巴巴工作，职级是P6",
        }
      );
      console.log("Qwen信息提取结果:", extractResult);
    } catch (error) {
      console.log("Qwen信息提取失败:", error.message);
    }
    console.log("");

    console.log("📋 测试4：模拟调用analyzeUserIntent");
    try {
      const intentResult = await aiServices.analyzeUserIntent("你好", {
        sessionContext: {},
        messageHistory: [],
      });
      console.log("原有意图分析结果:", intentResult);
    } catch (error) {
      console.log("原有意图分析失败:", error.message);
    }
    console.log("");
  } catch (error) {
    console.error("❌ AI服务测试失败:", error);
  }
}

testAIServices().catch(console.error);
