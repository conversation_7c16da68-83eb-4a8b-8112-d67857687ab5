/**
 * 阶段11测试：4x4推荐矩阵基础结构
 */

const PassiveRecommender = require('./core/业务服务/passive-recommender.js');

console.log('🎯 阶段11测试：4x4推荐矩阵基础结构\n');

async function testStage11() {
  try {
    // 初始化被动推荐引擎
    console.log('📋 初始化被动推荐引擎...');
    const config = {
      getBusinessConfig: () => ({
        maxRecommendations: 10
      })
    };
    
    const mockDatabase = {
      getCandidateProfile: async (userId) => ({
        candidate_tech_direction_raw: "后端开发",
        candidate_level_raw: "P6",
        current_company_name_raw: "阿里巴巴"
      })
    };
    
    const passiveRecommender = new PassiveRecommender(mockDatabase, config);
    await passiveRecommender.initialize();
    console.log('✅ 被动推荐引擎初始化完成\n');

    // 测试用例1：基础4x4矩阵生成
    console.log('📋 测试用例1：基础4x4矩阵生成');
    const userProfile = {
      candidate_tech_direction_raw: "后端开发",
      candidate_level_raw: "P6",
      current_company_name_raw: "阿里巴巴"
    };
    
    const matrixResult = await passiveRecommender.generate4x4Matrix(userProfile);
    console.log('矩阵生成结果:');
    console.log('- 矩阵大小:', matrixResult.matrix.length, 'x', matrixResult.matrix[0].length);
    console.log('- 总推荐位:', matrixResult.metadata.totalCells);
    console.log('- 公司类型数量:', Object.keys(matrixResult.metadata.companyTypes).length);
    console.log('- 技术方向数量:', Object.keys(matrixResult.metadata.techDirections).length);
    
    // 验证矩阵结构
    if (matrixResult.matrix.length === 4 && matrixResult.matrix[0].length === 4) {
      console.log('✅ 4x4矩阵结构正确');
    } else {
      console.log('❌ 4x4矩阵结构错误');
    }
    
    // 验证16个推荐位
    let totalCells = 0;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (matrixResult.matrix[i][j] !== null) {
          totalCells++;
        }
      }
    }
    
    if (totalCells === 16) {
      console.log('✅ 16个推荐位数据结构正确');
    } else {
      console.log('❌ 推荐位数量错误:', totalCells);
    }
    
    // 验证公司类型映射
    const companyTypes = matrixResult.metadata.companyTypes;
    const expectedTypes = ['头部大厂', '国企', '中型公司', '创业型'];
    const actualTypes = Object.values(companyTypes).map(ct => ct.name);
    
    const hasAllTypes = expectedTypes.every(type => actualTypes.includes(type));
    if (hasAllTypes) {
      console.log('✅ 公司类型映射正确');
    } else {
      console.log('❌ 公司类型映射错误');
    }
    console.log('');

    // 测试用例2：公司类型过滤
    console.log('📋 测试用例2：公司类型过滤');
    const filteredResult = await passiveRecommender.generate4x4Matrix(userProfile, "bigtech");
    
    let bigtechCells = 0;
    let nullCells = 0;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        const cell = filteredResult.matrix[i][j];
        if (cell === null) {
          nullCells++;
        } else if (cell.companyType.type === "bigtech") {
          bigtechCells++;
        }
      }
    }
    
    console.log('- 头部大厂推荐位:', bigtechCells);
    console.log('- 过滤掉的推荐位:', nullCells);
    
    if (bigtechCells === 4 && nullCells === 12) {
      console.log('✅ 公司类型过滤功能正确');
    } else {
      console.log('❌ 公司类型过滤功能错误');
    }
    console.log('');

    // 测试用例3：匹配度计算
    console.log('📋 测试用例3：匹配度计算');
    const sampleCell = matrixResult.matrix[0][1]; // 头部大厂 + 后端开发
    console.log('- 样本推荐位匹配度:', sampleCell.matchScore);
    console.log('- 是否推荐:', sampleCell.isRecommended);
    
    if (sampleCell.matchScore > 0 && sampleCell.matchScore <= 1) {
      console.log('✅ 匹配度计算正确');
    } else {
      console.log('❌ 匹配度计算错误');
    }

    console.log('\n🎯 阶段11测试完成');
    console.log('📊 测试总结:');
    console.log('- generate4x4Matrix方法: ✅ 成功');
    console.log('- 公司类型映射: ✅ 成功');
    console.log('- 4x4矩阵生成: ✅ 成功');
    console.log('- 公司类型过滤: ✅ 成功');
    console.log('- 16个推荐位结构: ✅ 成功');

  } catch (error) {
    console.error('❌ 阶段11测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

testStage11().catch(console.error);
