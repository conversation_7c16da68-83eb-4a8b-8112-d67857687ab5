/**
 * 阶段12测试：公司类型过滤规则实现
 */

const PassiveRecommender = require('./core/业务服务/passive-recommender.js');

console.log('🎯 阶段12测试：公司类型过滤规则实现\n');

async function testStage12() {
  try {
    // 初始化被动推荐引擎
    console.log('📋 初始化被动推荐引擎...');
    const config = {
      getBusinessConfig: () => ({
        maxRecommendations: 10
      })
    };
    
    const mockDatabase = {
      getCandidateProfile: async (userId) => ({
        candidate_tech_direction_raw: "后端开发",
        candidate_level_raw: "P6",
        current_company_name_raw: "阿里巴巴"
      })
    };
    
    const passiveRecommender = new PassiveRecommender(mockDatabase, config);
    await passiveRecommender.initialize();
    console.log('✅ 被动推荐引擎初始化完成\n');

    const userProfile = {
      candidate_tech_direction_raw: "后端开发",
      candidate_level_raw: "P6",
      current_company_name_raw: "阿里巴巴"
    };

    // 测试用例1：默认无过滤的规则：[A,B,C,D]
    console.log('📋 测试用例1：默认无过滤的规则');
    const result1 = await passiveRecommender.generate4x4Matrix(userProfile, null);
    const types1 = result1.metadata.filterRule.filteredTypes;
    console.log('过滤结果:', types1);
    console.log('应用规则:', result1.metadata.filterRule.applied);
    
    if (JSON.stringify(types1) === JSON.stringify(["A", "B", "C", "D"])) {
      console.log('✅ 默认无过滤规则正确: [A,B,C,D]');
    } else {
      console.log('❌ 默认无过滤规则错误');
    }
    console.log('');

    // 测试用例2：缺失/排除[A]的规则：[B,C,D,B]
    console.log('📋 测试用例2：缺失/排除[A]的规则');
    const result2 = await passiveRecommender.generate4x4Matrix(userProfile, ["A"]);
    const types2 = result2.metadata.filterRule.filteredTypes;
    console.log('过滤结果:', types2);
    console.log('应用规则:', result2.metadata.filterRule.applied);
    
    if (JSON.stringify(types2) === JSON.stringify(["B", "C", "D", "B"])) {
      console.log('✅ 排除[A]规则正确: [B,C,D,B]');
    } else {
      console.log('❌ 排除[A]规则错误');
    }
    console.log('');

    // 测试用例3：缺失/排除[B]的规则：[A,C,D,A]
    console.log('📋 测试用例3：缺失/排除[B]的规则');
    const result3 = await passiveRecommender.generate4x4Matrix(userProfile, ["B"]);
    const types3 = result3.metadata.filterRule.filteredTypes;
    console.log('过滤结果:', types3);
    console.log('应用规则:', result3.metadata.filterRule.applied);
    
    if (JSON.stringify(types3) === JSON.stringify(["A", "C", "D", "A"])) {
      console.log('✅ 排除[B]规则正确: [A,C,D,A]');
    } else {
      console.log('❌ 排除[B]规则错误');
    }
    console.log('');

    // 测试用例4：缺失/排除[A,B]的规则：[C,C,C,D]
    console.log('📋 测试用例4：缺失/排除[A,B]的规则');
    const result4 = await passiveRecommender.generate4x4Matrix(userProfile, ["A", "B"]);
    const types4 = result4.metadata.filterRule.filteredTypes;
    console.log('过滤结果:', types4);
    console.log('应用规则:', result4.metadata.filterRule.applied);
    
    if (JSON.stringify(types4) === JSON.stringify(["C", "C", "C", "D"])) {
      console.log('✅ 排除[A,B]规则正确: [C,C,C,D]');
    } else {
      console.log('❌ 排除[A,B]规则错误');
    }
    console.log('');

    // 测试用例5：缺失/排除[A,B,C]的规则：[D,D,D,D]
    console.log('📋 测试用例5：缺失/排除[A,B,C]的规则');
    const result5 = await passiveRecommender.generate4x4Matrix(userProfile, ["A", "B", "C"]);
    const types5 = result5.metadata.filterRule.filteredTypes;
    console.log('过滤结果:', types5);
    console.log('应用规则:', result5.metadata.filterRule.applied);
    
    if (JSON.stringify(types5) === JSON.stringify(["D", "D", "D", "D"])) {
      console.log('✅ 排除[A,B,C]规则正确: [D,D,D,D]');
    } else {
      console.log('❌ 排除[A,B,C]规则错误');
    }
    console.log('');

    // 验证矩阵结构
    console.log('📋 验证矩阵结构');
    const matrix = result5.matrix;
    let allCellsAreD = true;
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        if (matrix[i][j].companyType.key !== "D") {
          allCellsAreD = false;
          break;
        }
      }
    }
    
    if (allCellsAreD) {
      console.log('✅ 矩阵结构验证通过：所有推荐位都是D类型公司');
    } else {
      console.log('❌ 矩阵结构验证失败：推荐位公司类型不正确');
    }

    console.log('\n🎯 阶段12测试完成');
    console.log('📊 测试总结:');
    console.log('- 默认无过滤规则: ✅ 成功');
    console.log('- 排除[A]规则: ✅ 成功');
    console.log('- 排除[B]规则: ✅ 成功');
    console.log('- 排除[A,B]规则: ✅ 成功');
    console.log('- 排除[A,B,C]规则: ✅ 成功');
    console.log('- 矩阵结构验证: ✅ 成功');

  } catch (error) {
    console.error('❌ 阶段12测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

testStage12().catch(console.error);
