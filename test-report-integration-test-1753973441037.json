{"testId": "integration-test-1753973441037", "startTime": "2025-07-31T14:50:41.037Z", "endTime": "2025-07-31T14:56:38.175Z", "totalTestTime": 357138, "statistics": {"totalTests": 25, "passedTests": 20, "failedTests": 5, "testSuccessRate": 80, "totalRequests": 74, "successfulRequests": 73, "apiSuccessRate": 98.64864864864865, "avgResponseTime": 28012.297297297297, "errors": 0}, "compliance": {"apiSuccessRateTarget": 95, "apiSuccessRateActual": 98.64864864864865, "apiSuccessRatePassed": true, "avgResponseTimeTarget": 30000, "avgResponseTimeActual": 28012.297297297297, "avgResponseTimePassed": true}, "testResults": [{"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:50:41.060Z", "name": "系统健康检查", "category": "SYSTEM_HEALTH", "startTime": 1753973441041, "endTime": 1753973441060, "responseTime": 19, "status": "PASSED", "httpStatus": 200, "responseData": {"status": "healthy", "timestamp": "2025-07-31T14:50:41.057Z", "version": "1.0.0"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "包含status字段", "passed": true}, {"check": "status为healthy", "passed": true}, {"check": "响应时间<1000ms", "passed": true}]}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:50:52.142Z", "name": "硬编码回复测试 - 问候语", "input": {"message": "你好", "userEmail": "<EMAIL>", "sessionId": "e050a977-a2dc-4521-996f-110a50489a37"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "问候处理"], "expectedContent": "您考虑看看新机会吗？优质的职位还挺多的。", "startTime": 1753973441061, "category": "DUAL_MODEL_AI", "endTime": 1753973452141, "responseTime": 11080, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e050a977-a2dc-4521-996f-110a50489a37", "response": {"type": "first_greeting", "content": "您考虑看看新机会吗？优质的职位还挺多的。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_greeting"}}, "intent": "greeting", "timestamp": "2025-07-31T14:50:52.140Z"}, "analysis": {"hasResponse": true, "responseType": "first_greeting", "contentLength": 20, "featuresDetected": ["硬编码回复", "硬编码回复", "问候处理"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 问候处理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:50:59.234Z", "name": "硬编码回复测试 - 职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "c6f2bd9a-87a1-44ff-97a6-ac48ba751c94"}, "expectedModel": "hardcoded", "expectedFeatures": ["硬编码回复", "职位推荐"], "expectedContent": "我们当前合作了很多公司", "startTime": 1753973453143, "category": "DUAL_MODEL_AI", "endTime": 1753973459233, "responseTime": 6090, "httpStatus": 200, "responseData": {"success": true, "sessionId": "c6f2bd9a-87a1-44ff-97a6-ac48ba751c94", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T14:50:59.230Z"}, "analysis": {"hasResponse": true, "responseType": "first_job_inquiry", "contentLength": 72, "featuresDetected": ["硬编码回复", "硬编码回复", "职位推荐"], "isHardcoded": true, "modelUsed": "hardcoded", "contentMatches": true}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 硬编码回复", "passed": true}, {"check": "功能检测: 职位推荐", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:51:17.387Z", "name": "<PERSON>wen意图分析 + DeepSeek回复生成", "input": {"message": "我关注大模型方向，最近在找工作", "userEmail": "<EMAIL>", "sessionId": "7e50b7dc-eca2-474d-9ebf-fd63c308b89a"}, "expectedModel": "both", "expectedFeatures": ["意图分析", "对话生成", "AI推理"], "startTime": 1753973460235, "category": "DUAL_MODEL_AI", "endTime": 1753973477386, "responseTime": 17151, "httpStatus": 200, "responseData": {"success": true, "sessionId": "7e50b7dc-eca2-474d-9ebf-fd63c308b89a", "response": {"type": "first_ai_response", "content": "您好！很高兴了解到您正在关注大模型方向并寻找新机会。为了更好地为您推荐合适的岗位，能否请您补充以下信息：\n1. 您目前主要使用的技术栈或框架(如PyTorch/TensorFlow等)\n2. 当前公司及职级情况\n3. 期望的工作地点和薪资范围\n\n这些信息将帮助我为您提供更精准的职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T14:51:17.382Z"}, "analysis": {"hasResponse": true, "responseType": "first_ai_response", "contentLength": 144, "featuresDetected": ["AI推理", "意图分析", "对话生成", "AI推理"], "isHardcoded": false, "modelUsed": "deepseek"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 意图分析", "passed": true}, {"check": "功能检测: 对话生成", "passed": true}, {"check": "功能检测: AI推理", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:51:29.121Z", "name": "复杂信息提取测试", "input": {"message": "我是Python算法工程师，有5年经验，期望薪资40k，想在北京工作", "userEmail": "<EMAIL>", "sessionId": "02707655-8262-45e6-a975-1e6804709a2a"}, "expectedModel": "qwen_analysis", "expectedFeatures": ["信息提取", "技术栈识别", "薪资分析", "地理位置"], "startTime": 1753973478388, "category": "DUAL_MODEL_AI", "endTime": 1753973489120, "responseTime": 10732, "httpStatus": 200, "responseData": {"success": true, "sessionId": "02707655-8262-45e6-a975-1e6804709a2a", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T14:51:27.574Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T14:51:29.116Z"}, "analysis": {"hasResponse": true, "responseType": "error", "contentLength": 21, "featuresDetected": ["技术栈识别", "薪资分析", "地理位置"], "isHardcoded": false, "modelUsed": "unknown"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含AI响应", "passed": true}, {"check": "响应时间<30秒", "passed": true}, {"check": "内容长度>10字符", "passed": true}, {"check": "功能检测: 信息提取", "passed": false}, {"check": "功能检测: 技术栈识别", "passed": true}, {"check": "功能检测: 薪资分析", "passed": true}, {"check": "功能检测: 地理位置", "passed": true}], "status": "FAILED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:51:38.969Z", "name": "主动触发 - 直接职位询问", "input": {"message": "有什么职位推荐吗", "userEmail": "<EMAIL>", "sessionId": "6097e6fb-19cc-4850-bdf2-d13ca1815698"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753973490125, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973498967, "responseTime": 8842, "httpStatus": 200, "responseData": {"success": true, "sessionId": "6097e6fb-19cc-4850-bdf2-d13ca1815698", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T14:51:38.961Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["麻烦您告知", "告知一下您的信息", "便于我能够给您", "推荐合适的职位", "您的信息点"], "responseType": "first_job_inquiry"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:51:47.298Z", "name": "主动触发 - 工作机会咨询", "input": {"message": "我想了解一下工作机会", "userEmail": "<EMAIL>", "sessionId": "2e0d5607-a828-4090-ac50-e7824a1830bd"}, "triggerType": "active", "expectedTrigger": true, "startTime": 1753973499970, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973507298, "responseTime": 7328, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2e0d5607-a828-4090-ac50-e7824a1830bd", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T14:51:46.321Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T14:51:47.295Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:51:55.386Z", "name": "关键词触发 - 技术栈提及", "input": {"message": "我是Python开发工程师", "userEmail": "<EMAIL>", "sessionId": "238fbc77-e98f-4d29-8af6-e3d9533795b1"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753973508300, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973515386, "responseTime": 7086, "httpStatus": 200, "responseData": {"success": true, "sessionId": "238fbc77-e98f-4d29-8af6-e3d9533795b1", "response": {"type": "professional_decline", "content": "我了解您在Python开发工程师方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有Python开发工程师的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "Python开发工程师"}}}, "intent": "profile_update", "timestamp": "2025-07-31T14:51:55.382Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["了解您"], "responseType": "professional_decline"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:52:03.965Z", "name": "关键词触发 - 公司经历", "input": {"message": "我在腾讯工作过2年", "userEmail": "<EMAIL>", "sessionId": "94616215-fee1-4e61-a49a-19ec99dee8c1"}, "triggerType": "keyword", "expectedTrigger": true, "startTime": 1753973516387, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973523965, "responseTime": 7578, "httpStatus": 200, "responseData": {"success": true, "sessionId": "94616215-fee1-4e61-a49a-19ec99dee8c1", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T14:52:03.033Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T14:52:03.959Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:52:13.239Z", "name": "上下文触发 - 薪资期望", "input": {"message": "期望薪资在30k左右", "userEmail": "<EMAIL>", "sessionId": "fa1b1021-3963-4935-8a8b-e22926bb0b57"}, "triggerType": "context", "expectedTrigger": true, "startTime": 1753973524966, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973533239, "responseTime": 8273, "httpStatus": 200, "responseData": {"success": true, "sessionId": "fa1b1021-3963-4935-8a8b-e22926bb0b57", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T14:52:11.636Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T14:52:13.235Z"}, "triggerAnalysis": {"triggered": false, "triggerKeywords": [], "responseType": "error"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:52:35.856Z", "name": "非触发 - 无关内容", "input": {"message": "今天天气真好", "userEmail": "<EMAIL>", "sessionId": "2b6521bf-005e-4d90-ac34-9b84ccd2b7a5"}, "triggerType": "none", "expectedTrigger": false, "startTime": 1753973534240, "category": "INFO_COLLECTION_TRIGGER", "endTime": 1753973555855, "responseTime": 21615, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2b6521bf-005e-4d90-ac34-9b84ccd2b7a5", "response": {"type": "first_ai_response", "content": "您好！很高兴在这个好天气与您交流。作为专业的招聘顾问，我想先了解一下您的技术背景和求职需求：\n\n1. 您目前主要使用的技术栈是什么？（比如Java/Python/前端等）\n2. 目前在什么公司担任什么级别的职位？\n3. 对下一份工作有什么具体的期望吗？\n\n这样我可以更精准地为您推荐合适的机会。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "greeting", "timestamp": "2025-07-31T14:52:35.849Z"}, "triggerAnalysis": {"triggered": true, "triggerKeywords": ["您的技术", "技术栈"], "responseType": "first_ai_response"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "触发正确性", "passed": false}], "status": "FAILED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:52:43.412Z", "name": "第三方推荐功能测试", "category": "THIRD_PARTY_RECOMMENDATION", "input": {"message": "我想了解一些外部的职位推荐", "userEmail": "<EMAIL>", "sessionId": "8c0a7c9b-aee1-438e-8b19-0e6f45222488"}, "startTime": 1753973556861, "endTime": 1753973563412, "responseTime": 6551, "httpStatus": 200, "responseData": {"success": true, "sessionId": "8c0a7c9b-aee1-438e-8b19-0e6f45222488", "response": {"type": "first_job_inquiry", "content": "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。", "metadata": {"isFirstResponse": true, "responseSource": "hardcoded_job_inquiry", "hasFollowUpMessage": true}}, "intent": "job_search", "timestamp": "2025-07-31T14:52:43.410Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐内容", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:52:54.703Z", "name": "技术栈维度推荐", "input": {"message": "我精通Java、Spring Boot、MySQL，有什么推荐", "userEmail": "<EMAIL>", "sessionId": "2f5cc6bb-60cc-483b-87b7-df19a16308ad"}, "dimension": "technology", "startTime": 1753973563413, "category": "RECOMMENDATION_MATRIX", "endTime": 1753973574703, "responseTime": 11290, "httpStatus": 200, "responseData": {"success": true, "sessionId": "2f5cc6bb-60cc-483b-87b7-df19a16308ad", "response": {"type": "professional_decline", "content": "我了解您在Java、Spring Boot、MySQL方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有Java、Spring Boot、MySQL的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "Java、Spring Boot、MySQL"}}}, "intent": "profile_update", "timestamp": "2025-07-31T14:52:54.698Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:53:05.667Z", "name": "经验级别维度推荐", "input": {"message": "我有5年开发经验，想找高级工程师职位", "userEmail": "<EMAIL>", "sessionId": "249d8ebc-0d8b-4ba2-9778-f05518ec0e44"}, "dimension": "experience", "startTime": 1753973575704, "category": "RECOMMENDATION_MATRIX", "endTime": 1753973585667, "responseTime": 9963, "httpStatus": 200, "responseData": {"success": true, "sessionId": "249d8ebc-0d8b-4ba2-9778-f05518ec0e44", "response": {"type": "professional_decline", "content": "我了解您在开发方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有开发的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "开发", "工作经验": "5年", "当前职级": "高级"}}}, "intent": "profile_update", "timestamp": "2025-07-31T14:53:05.663Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:53:15.276Z", "name": "行业领域维度推荐", "input": {"message": "我想在金融科技领域发展", "userEmail": "<EMAIL>", "sessionId": "5ef75d89-44f2-47be-805a-3416f1e10e6e"}, "dimension": "industry", "startTime": 1753973586667, "category": "RECOMMENDATION_MATRIX", "endTime": 1753973595276, "responseTime": 8609, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5ef75d89-44f2-47be-805a-3416f1e10e6e", "response": {"type": "professional_decline", "content": "我了解您在金融科技领域方面的经验。不过我是专注于AI算法领域的招聘顾问，主要服务于机器学习、深度学习、推荐算法、NLP、CV等AI相关职位。暂时没有金融科技领域的职位，所以没法给您推荐职位啦。", "metadata": {"declineReason": "tech_mismatch", "extractedInfo": {"技术方向": "金融科技领域", "业务场景": "金融"}}}, "intent": "profile_update", "timestamp": "2025-07-31T14:53:15.273Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:53:26.662Z", "name": "地理位置维度推荐", "input": {"message": "我希望在北京或上海工作", "userEmail": "<EMAIL>", "sessionId": "edbe2207-a322-41f0-9458-5d0ad70b3917"}, "dimension": "location", "startTime": 1753973596278, "category": "RECOMMENDATION_MATRIX", "endTime": 1753973606662, "responseTime": 10384, "httpStatus": 200, "responseData": {"success": true, "sessionId": "edbe2207-a322-41f0-9458-5d0ad70b3917", "response": {"type": "error", "content": "抱歉，系统遇到了一些技术问题，请稍后再试。", "metadata": {"error": true, "timestamp": "2025-07-31T14:53:25.627Z"}}, "intent": "profile_update", "timestamp": "2025-07-31T14:53:26.658Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "包含推荐逻辑", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:53:47.583Z", "name": "被动推荐功能测试", "category": "PASSIVE_RECOMMENDATION", "input": {"message": "我目前还没有明确的求职意向，只是了解一下市场", "userEmail": "<EMAIL>", "sessionId": "7165abe5-4b20-4e1e-a3c5-7372335d175b"}, "startTime": 1753973607666, "endTime": 1753973627582, "responseTime": 19916, "httpStatus": 200, "responseData": {"success": true, "sessionId": "7165abe5-4b20-4e1e-a3c5-7372335d175b", "response": {"type": "first_ai_response", "content": "您好！作为专业的招聘顾问，我很乐意为您提供市场情况参考。为了更好地评估适合您的机会，能否请您分享以下信息：  \n\n1. 您的技术栈或主要技术方向是什么？  \n2. 目前所在公司及职级（如：中级/高级工程师）？  \n3. 如果考虑机会，您更关注哪些方面（薪资、技术成长、团队等）？  \n\n这些信息将帮助我为您提供更有针对性的建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T14:53:47.580Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "提供市场信息", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:54:11.765Z", "name": "用户主动终止对话", "input": {"message": "谢谢，我不需要更多信息了", "userEmail": "<EMAIL>", "sessionId": "e581dc64-4243-49cd-ae7e-b8777a381bf1"}, "startTime": 1753973627583, "category": "CONVERSATION_TERMINATION", "endTime": 1753973651765, "responseTime": 24182, "httpStatus": 200, "responseData": {"success": true, "sessionId": "e581dc64-4243-49cd-ae7e-b8777a381bf1", "response": {"type": "ai_understanding", "content": "我完全理解您现在的决定。作为专业的招聘助手，我会尊重您的选择。如果您未来在AI算法领域有任何职业发展方面的需求，随时欢迎回来咨询。祝您一切顺利！", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "谢谢，我不需要更多信息了"}}, "intent": "unknown", "timestamp": "2025-07-31T14:54:11.759Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:54:37.120Z", "name": "重复无效输入终止", "input": {"message": "不知道", "userEmail": "<EMAIL>", "sessionId": "d9722d27-d955-4849-8f6d-03cdf722f906"}, "startTime": 1753973652767, "category": "CONVERSATION_TERMINATION", "endTime": 1753973677120, "responseTime": 24353, "httpStatus": 200, "responseData": {"success": true, "sessionId": "d9722d27-d955-4849-8f6d-03cdf722f906", "response": {"type": "ai_understanding", "content": "您好Katrina！我理解您可能对AI招聘领域还不太熟悉。作为专业的AI招聘助手，我可以帮助您：\n\n1. 分析您的技术背景（比如算法/开发/数据方向）\n2. 评估适合的职业发展路径\n3. 提供行业薪资参考\n\n您目前是正在寻找AI相关的工作机会吗？或者想了解哪个具体方向的发展建议？", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "不知道"}}, "intent": "unknown", "timestamp": "2025-07-31T14:54:37.114Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:54:56.785Z", "name": "明确拒绝服务", "input": {"message": "我不想找工作了", "userEmail": "<EMAIL>", "sessionId": "56aa8c87-d45b-47bc-89aa-20439bd9fc06"}, "startTime": 1753973678121, "category": "CONVERSATION_TERMINATION", "endTime": 1753973696785, "responseTime": 18664, "httpStatus": 200, "responseData": {"success": true, "sessionId": "56aa8c87-d45b-47bc-89aa-20439bd9fc06", "response": {"type": "first_ai_response", "content": "我理解您可能对当前求职状态有些想法转变。为了更好地了解您的需求，能否分享一下：  \n\n1. 您目前的技术栈或主要技术方向是什么？  \n2. 当前的职级和所在公司类型（如互联网/金融等）？  \n3. 如果未来考虑机会，您的薪资期望范围？  \n\n这些信息能帮助我为您提供更有针对性的建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T14:54:56.783Z"}, "assertions": [{"check": "HTTP状态200", "passed": true}, {"check": "响应成功", "passed": true}, {"check": "适当处理终止", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:54:57.799Z", "name": "空消息处理", "input": {"message": "", "userEmail": "<EMAIL>", "sessionId": "d7e0de0f-7627-4a04-87c7-8fb01320701c"}, "startTime": 1753973697788, "category": "ERROR_HANDLING", "endTime": 1753973697798, "responseTime": 10, "status": "PASSED", "error": "Request failed with status code 400"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:55:35.784Z", "name": "超长消息处理", "input": {"message": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "userEmail": "<EMAIL>", "sessionId": "fd9a59a4-d30b-4628-9fd6-9d59c4ab1d83"}, "startTime": 1753973698800, "category": "ERROR_HANDLING", "endTime": 1753973735784, "responseTime": 36984, "httpStatus": 200, "responseData": {"success": true, "sessionId": "fd9a59a4-d30b-4628-9fd6-9d59c4ab1d83", "response": {"type": "ai_understanding", "content": "您好，我注意到您发送了一长串\"A\"字符。作为专业的AI招聘助手，我理解您可能在测试或误触键盘。为了更好地帮助您，能否请您：\n\n1. 说明您具体的求职需求（如技术方向/职位类型）\n2. 或者分享您的技术背景（如编程语言/工作经验）？\n\n例如您可以告诉我：\n\"我是3年经验的Java后端工程师\"\n\"想找北京地区的机器学习岗位\"\n\"对比几个AI公司的薪资范围\"\n\n我会根据您提供的信息，给出针对性的职业建议和求职策略。期待您的具体需求说明！", "suggestions": ["我想找工作", "推荐职位给我", "更新个人信息", "了解薪资行情"], "metadata": {"responseSource": "ai_understanding", "originalMessage": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"}}, "intent": "unknown", "timestamp": "2025-07-31T14:55:35.779Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:55:36.793Z", "name": "特殊字符处理", "input": {"message": "!@#$%^&*()_+{}|:\"<>?[]\\;',./", "userEmail": "<EMAIL>", "sessionId": "37f2ec25-9f41-4f6d-aa9d-5fde4607f4d0"}, "startTime": 1753973736784, "category": "ERROR_HANDLING", "endTime": 1753973736793, "responseTime": 9, "httpStatus": 200, "responseData": {"success": false, "error": "检测到潜在的SQL注入攻击", "response": {"type": "error_fallback", "content": "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。", "suggestions": ["重新开始", "联系客服", "查看帮助"]}, "timestamp": "2025-07-31T14:55:36.791Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:55:50.783Z", "name": "无效邮箱格式", "input": {"message": "我想找工作", "userEmail": "invalid-email", "sessionId": "5fc02220-372c-4bfb-9d9c-b4a8302d8f54"}, "startTime": 1753973737794, "category": "ERROR_HANDLING", "endTime": 1753973750782, "responseTime": 12988, "httpStatus": 200, "responseData": {"success": true, "sessionId": "5fc02220-372c-4bfb-9d9c-b4a8302d8f54", "response": {"type": "first_ai_response", "content": "您好！我是招聘助手Katrina，很高兴为您提供求职帮助。为了更好地为您推荐合适的职位，请问您能分享以下信息吗：\n1. 您的技术栈或专业方向（如Java/Python/前端等）\n2. 当前工作年限和职级\n3. 期望的工作地点和薪资范围\n\n这些信息将帮助我为您提供更精准的职业建议。", "metadata": {"isFirstResponse": true, "responseSource": "ai_inference"}}, "intent": "job_search", "timestamp": "2025-07-31T14:55:50.777Z"}, "assertions": [{"check": "HTTP状态200或400", "passed": true}, {"check": "系统未崩溃", "passed": true}, {"check": "有错误处理响应", "passed": true}], "status": "PASSED"}, {"testId": "integration-test-1753973441037", "timestamp": "2025-07-31T14:56:38.175Z", "name": "高并发压力测试", "category": "HIGH_CONCURRENCY_SUMMARY", "startTime": 1753973751784, "endTime": 1753973798174, "responseTime": 46390, "totalRequests": 50, "successfulRequests": 50, "failedRequests": 0, "successRate": 100, "status": "PASSED"}]}